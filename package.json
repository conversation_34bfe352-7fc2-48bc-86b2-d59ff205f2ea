{"id": "shopro", "name": "shopro", "displayName": "Mall", "version": "1.0.1", "description": "翠柳科技商城", "scripts": {"prettier": "prettier --write  \"{pages,sheep}/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "prepare": "husky install"}, "repository": "https://github.com/sheepjs/shop.git", "keywords": ["商城", "B2C", "shopro", "商城模板"], "author": "", "license": "MIT", "bugs": {"url": "https://github.com/sheepjs/shop/issues"}, "homepage": "https://github.com/dcloudio/hello-uniapp#readme", "dcloudext": {"category": ["前端页面模板", "uni-app前端项目模板"], "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "u", "aliyun": "u"}, "client": {"App": {"app-vue": "y", "app-nvue": "u"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "u", "百度": "u", "字节跳动": "u", "QQ": "u", "京东": "u"}, "快应用": {"华为": "u", "联盟": "u"}, "Vue": {"vue2": "u", "vue3": "y"}}}}, "dependencies": {"@dcloudio/uni-app": "3.0.0-3061820230117001", "@dcloudio/uni-app-plus": "3.0.0-3061820230117001", "@dcloudio/uni-components": "3.0.0-3061820230117001", "@dcloudio/uni-h5": "3.0.0-3061820230117001", "@dcloudio/uni-mp-alipay": "3.0.0-3061820230117001", "@dcloudio/uni-mp-baidu": "3.0.0-3061820230117001", "@dcloudio/uni-mp-kuaishou": "3.0.0-3061820230117001", "@dcloudio/uni-mp-lark": "3.0.0-3061820230117001", "@dcloudio/uni-mp-qq": "3.0.0-3061820230117001", "@dcloudio/uni-mp-toutiao": "3.0.0-3061820230117001", "@dcloudio/uni-mp-weixin": "3.0.0-3061820230117001", "@dcloudio/uni-quickapp-webview": "3.0.0-3061820230117001", "@dcloudio/uni-ui": "1.4.24", "@hyoga/uni-socket.io": "^1.0.1", "base-64": "^1.0.0", "dayjs": "^1.11.6", "js-base64": "^3.7.5", "lodash": "^4.17.21", "luch-request": "^3.0.8", "pinia": "2.0.24", "pinia-plugin-persist-uni": "^1.2.0", "qs-canvas": "^1.0.11", "vue": "3.2.45", "weixin-js-sdk": "^1.6.0"}, "devDependencies": {"@dcloudio/types": "3.2.8", "@dcloudio/uni-automator": "3.0.0-3061820230117001", "@dcloudio/uni-cli-shared": "3.0.0-3061820230117001", "@dcloudio/uni-stacktracey": "3.0.0-3061820230117001", "@dcloudio/vite-plugin-uni": "3.0.0-3061820230117001", "husky": "8.0.3", "prettier": "^2.7.1", "sass": "1.57.1", "vconsole": "^3.15.0", "vite": "3.2.4"}, "uni-app": {"scripts": {"mp-dingtalk": {"title": "钉钉小程序", "env": {"UNI_PLATFORM": "mp-alipay"}, "define": {"MP-DINGTALK": true}}}}}