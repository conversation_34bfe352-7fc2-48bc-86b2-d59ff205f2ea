.cu-markdown {
  position: relative;
  z-index: 1;
  &.selectable {
    cursor: auto;
    user-select: text;
  }
  inline {
    display: inline-block;
  }

  .list {
    .list-item {
      line-height: 1.8;
      .list {
        margin-left: 1.28571em;
        .ui-title {
          transform: scale(0.6);
          &:before {
            content: '\e716';
          }
        }
      }
    }
    .list-item-p {
      position: relative;
      padding-left: 1.5em;
      .list-item-t {
        display: block;
        width: 1.3em;
        text-align: center;
        position: absolute;
        left: 0;
      }
    }
  }
  .md-table + .md-table {
    margin-top: 30rpx;
  }
}

.paragraph {
  margin: 0 0 40rpx;
  line-height: 1.8;
}

.blockquote {
  @extend .paragraph;
  padding: 20rpx 30rpx;
  border-left-style: solid;
  border-left-width: 10rpx;
  border-color: var(--ui-Border);
  background: none repeat scroll 0 0 rgba(102, 128, 153, 0.05);

  .paragraph {
    margin-bottom: 30rpx;
  }

  .paragraph:last-child {
    margin-bottom: 0;
  }
}
