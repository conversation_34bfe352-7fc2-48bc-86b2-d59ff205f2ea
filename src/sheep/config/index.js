// 开发环境配置
export let baseUrl;
export let version;
export let aiUrl;
if (process.env.NODE_ENV === 'development') {
  baseUrl = import.meta.env.SHOPRO_DEV_BASE_URL;
} else {
  baseUrl = import.meta.env.SHOPRO_BASE_URL;
}
aiUrl = import.meta.env.SHOPRO_AI_URL;
version = import.meta.env.SHOPRO_VERSION;
export const apiPath = import.meta.env.SHOPRO_API_PATH;

export const staticUrl = import.meta.env.SHOPRO_STATIC_URL;

export const aiAppKey = import.meta.env.CHAT_MESSAGE_APP_KEY;

export default {
  aiAppKey,
  aiUrl,
  baseUrl,
  apiPath,
  staticUrl,
};
