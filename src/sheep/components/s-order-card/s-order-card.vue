<template>
  <view class="ss-order-container">
    <!-- 全部订单按钮 -->
    <view
      class="all-orders-btn"
      @tap="sheep.$router.go('/pages/order/list')"
      hover-class="all-orders-btn-hover"
    >
      <view class="btn-content">
        <view class="btn-icon">
          <text class="cicon-order-o"></text>
        </view>
        <view class="btn-text">我的订单</view>
        <view class="btn-arrow">
          <text>全部</text>
          <uni-icons type="right" size="14" color="#333333"></uni-icons>
        </view>
      </view>
    </view>

    <!-- 订单状态菜单 -->
    <view class="ss-order-menu-wrap ss-flex ss-col-center">
      <view
        class="menu-item ss-flex-1 ss-flex-col ss-row-center ss-col-center"
        v-for="item in orderMap"
        :key="item.title"
        @tap="sheep.$router.go(item.path, { type: item.value })"
        hover-class="menu-item-hover"
      >
        <uni-badge
          class="uni-badge-left-margin"
          :text="numData[item.type]"
          absolute="rightTop"
          size="small"
          :max-num="99"
        >
          <view class="icon-wrapper">
            <text class="item-icon" :class="item.icon"></text>
            <!-- <image class="item-icon" :src="item.icon" mode="aspectFit"></image> -->
          </view>
        </uni-badge>
        <view class="menu-title">{{ item.title }}</view>
      </view>
    </view>
  </view>
</template>

<script setup>
  /**
   * 装修组件 - 订单菜单组
   */
  import sheep from '@/sheep';
  import { computed } from 'vue';

  const orderMap = [
    {
      title: '待付款',
      value: '1',
      icon: 'cicon-moneybag-o',
      path: '/pages/order/list',
      type: 'unpaid',
    },
    {
      title: '待发货',
      value: '2',
      icon: 'cicon-present-o',
      path: '/pages/order/list',
      type: 'nosend',
    },
    {
      title: '待收货',
      value: '3',
      icon: 'cicon-deliver-o',
      path: '/pages/order/list',
      type: 'noget',
    },
    {
      title: '待评价',
      value: '4',
      icon: 'cicon-popover-o',
      path: '/pages/order/list',
      type: 'nocomment',
    },
    {
      title: '退款/售后',
      value: '0',
      icon: 'cicon-countdown-o',
      path: '/pages/order/aftersale/list',
      type: 'gift',
    },
    // {
    //   title: '全部订单',
    //   value: '0',
    //   icon: '/static/user/all_order.png',
    //   path: '/pages/order/list',
    // },
  ];

  const numData = computed(() => sheep.$store('user').numData);
</script>

<style lang="scss" scoped>
  .ss-order-container {
    margin: 20rpx;
    background-color: #ffffff;
    box-shadow: 0 0 10rpx 0 rgba(0, 0, 0, 0.05);
    border-radius: 16rpx;
  }

  // 全部订单按钮样式
  .all-orders-btn {
    background-color: #ffffff;
    border-bottom: 1rpx solid #f0f0f0;
    transition: all 0.2s ease;

    &-hover {
      background-color: rgba(233, 180, 97, 0.03);
    }

    .btn-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20rpx ;

      .btn-icon {
        width: 40rpx;
        height: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10rpx;

        .cicon-order-o {
          font-size: 32rpx;
        }
      }

      .btn-text {
        flex: 1;
        font-size: 28rpx;
        font-weight: 500;
        color: #333333;
      }

      .btn-arrow {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24rpx;
        color: #333333;
        gap: 10rpx;
      }
    }
  }

  .ss-order-menu-wrap {
    padding: 16rpx 0;
    
    .menu-item {
      height: 160rpx;
      position: relative;
      z-index: 10;
      transition: all 0.2s;
      
      &-hover {
        transform: scale(0.96);
        opacity: 0.8;
      }
      
      .icon-wrapper {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        background: rgba(249, 249, 249, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 10rpx;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);

        .item-icon {
          font-size: 44rpx;
        }
      }
      
      .menu-title {
        font-size: 24rpx;
        margin-top: 12rpx;
        line-height: 1.2;
        color: #333333;
        font-weight: 500;
      }
      
      .item-icon {
        width: 44rpx;
        height: 44rpx;
      }
    }
  }
  
  // 修改uni-badge样式
  :deep(.uni-badge) {
    transform: scale(0.85) translateX(6rpx) translateY(-6rpx);
  }
</style>
