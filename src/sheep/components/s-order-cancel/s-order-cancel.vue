<template>
	<view class="cancel-delivery-popup">
		<!-- 图标区域 -->
		<view class="popup-icon">
			<view class="icon-wrapper">
				<image :src="sheep.$url.static('/2025/06/055a97d8957385410884c229ab9207875d%E8%AE%A2%E5%8D%95-%E5%8F%96%E6%B6%88%E8%AE%A2%E5%8D%95.png')"></image>
			</view>
		</view>

		<!-- 内容区域 -->
		<view class="popup-content">
			<view class="cancel-title">取消订单</view>
			<view class="cancel-subtitle">确认要取消此货到付款订单吗？</view>

			<!-- 简化的订单信息 -->
			<view class="order-info">
				<view class="info-item">
					<text class="info-label">订单状态：</text>
					<text class="info-value">商品未发货</text>
				</view>
				<view class="info-item">
					<text class="info-label">支付类型：</text>
					<text class="info-value highlight">货到付款</text>
				</view>
			</view>
		</view>

		<!-- 按钮组 -->
		<view class="btn-group">
			<button class="ss-reset-button cancel-btn" @tap="emits('closePopup')">
				<text class="btn-text">再想想</text>
			</button>
			<button class="ss-reset-button confirm-btn ui-BG-Main-Gradient" @tap="emits('confirmCancelDelivery')">
				<text class="btn-text">确认取消</text>
			</button>
		</view>
	</view>
</template>
<script setup>
	import sheep from '@/sheep';
	const emits = defineEmits(['confirmCancelDelivery', 'closePopup']);
</script>

<style lang="scss" scoped>
	/* 取消货到付款弹窗样式 - 简约版 */
	.cancel-delivery-popup {
		width: 100%;
		background: #ffffff;
		border-radius: 20rpx;
		overflow: hidden;
		position: relative;
		box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.12);
		padding-bottom: calc(env(safe-area-inset-bottom) / 2);

		// 图标区域
		.popup-icon {
			padding: 50rpx 0 30rpx;
			display: flex;
			justify-content: center;
			align-items: center;

			.icon-wrapper {
				width: 120rpx;
				height: 120rpx;
				background: linear-gradient(135deg, #fff3e6 0%, #ffe8d1 100%);
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				position: relative;

				image {
					width: 100%;
					height: 100%;
				}

				&::before {
					content: '';
					position: absolute;
					width: 140rpx;
					height: 140rpx;
					background: linear-gradient(135deg, rgba(255, 96, 0, 0.1) 0%, rgba(255, 133, 51, 0.05) 100%);
					border-radius: 50%;
					z-index: -1;
				}

				.cancel-icon {
					font-size: 60rpx;
					filter: grayscale(0.2);
				}
			}
		}

		// 内容区域
		.popup-content {
			padding: 0 40rpx 30rpx;
			text-align: center;

			.cancel-title {
				font-size: 34rpx;
				font-weight: 700;
				color: #333;
				margin-bottom: 16rpx;
				letter-spacing: 0.5rpx;
			}

			.cancel-subtitle {
				font-size: 28rpx;
				color: #666;
				margin-bottom: 30rpx;
				line-height: 1.4;
			}

			// 简化的订单信息
			.order-info {
				background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
				border-radius: 12rpx;
				padding: 20rpx 24rpx;
				margin-bottom: 20rpx;
				border: 1rpx solid #f0f0f0;
				text-align: left;

				.info-item {
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: 8rpx 0;

					&:first-child {
						border-bottom: 1rpx solid #f0f0f0;
						padding-bottom: 12rpx;
						margin-bottom: 8rpx;
					}

					.info-label {
						font-size: 26rpx;
						color: #666;
						font-weight: 500;
					}

					.info-value {
						font-size: 26rpx;
						color: #333;
						font-weight: 600;

						&.highlight {
							color: #ff6000;
							font-weight: 700;
						}
					}
				}
			}

			// 温馨提示
			.cancel-tips {
				background: linear-gradient(135deg, #fff8f0 0%, #ffffff 100%);
				border-radius: 12rpx;
				padding: 20rpx 24rpx;
				border-left: 4rpx solid #ff6000;
				margin-bottom: 20rpx;
				text-align: left;

				.tips-text {
					font-size: 26rpx;
					color: #666;
					line-height: 1.5;
				}
			}
		}

		// 按钮组
		.btn-group {
			display: flex;
			padding: 0 30rpx 40rpx;
			gap: 20rpx;

			.cancel-btn,
			.confirm-btn {
				flex: 1;
				height: 88rpx;
				border-radius: 44rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-weight: 600;
				transition: all 0.2s ease;
				border: none;

				.btn-text {
					font-size: 30rpx;
				}
			}

			.cancel-btn {
				background: #f5f5f5;
				color: #666;

				&:active {
					background: #e8e8e8;
					transform: scale(0.98);
				}
			}

			.confirm-btn {
				color: #fff;
				box-shadow: 0 6rpx 20rpx rgba(255, 96, 0, 0.25);

				&:active {
					transform: scale(0.98);
					box-shadow: 0 3rpx 10rpx rgba(255, 96, 0, 0.35);
				}
			}
		}
	}
</style>
