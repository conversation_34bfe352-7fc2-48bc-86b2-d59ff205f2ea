<template>
	<view class="wechat-login-container">
		<view class="head-box ss-flex-col">
			<view class="ss-flex ss-m-b-20">
				<view class="head-title ss-m-r-40 head-title-animation">登录</view>
			</view>
		</view>
		<!-- TODO -->
		<view>欢迎您登录【XXXX】</view>
		<view style="margin-top: 140rpx">
			<view class="agreement-box ss-flex" style="margin-bottom: 16rpx">
				<checkbox-group @change="onChange" class="ss-flex ss-col-center">
					<label class="radio ss-flex">
						<checkbox :checked="state.protocol" color="var(&#45;&#45;ui-BG-Main)" style="transform: scale(0.6)" />
					</label>
				</checkbox-group>
				<view class="ss-flex ss-col-center ss-m-l-8">
					我已阅读并遵守
					<view class="tcp-text" @tap.stop="onProtocol(appInfo.user_protocol.id, appInfo.user_protocol.title, appInfo.user_protocol.value)"> 《{{ appInfo.user_protocol.title }}》 </view>
				</view>
			</view>
			<button v-if="!state.protocol" @click="handleClick" class="ss-reset-button buy-btn ui-Success-Main">
				<view class="ss-flex">
					<!-- <image class="auto-login-img" :src="sheep.$url.static('/image/weixin.svg')" /> -->
					<view>手机号快捷登录</view>
				</view>
			</button>
			<button v-else class="ss-reset-button buy-btn ui-Success-Main" open-type="getPhoneNumber" @getphonenumber="wechatLogin">
				<view class="ss-flex">
					<!-- <image class="auto-login-img" :src="sheep.$url.static('/image/weixin.svg')" /> -->
					<view>手机号快捷登录</view>
				</view>
			</button>
			<view style="height: 16rpx"></view>
			<button class="ss-reset-button cancel-btn ui-Shadow-Pioneer-Main" @tap="closeAuthModal">
				<view class="ss-flex">
					<!--        <image class="auto-login-img" :src="sheep.$url.static('/image/weixin.svg')"/>-->
					<view>暂不登录</view>
				</view>
			</button>
		</view>
		<view class="agreement-text ss-flex">
			登录遇到问题？点此
			<button class="btnStyle" open-type="contact">联系客服</button>
		</view>
	</view>
</template>

<script setup>
	import sheep from '@/sheep';
	import { computed, reactive, getCurrentInstance } from 'vue';
	import { onLoad, onShow } from '@dcloudio/uni-app';
	import { closeAuthModal } from '@/sheep/hooks/useModal';
	const appInfo = computed(() => sheep.$store('app').info);
	const state = reactive({
		protocol: false,
		orderId: '',
	});
	const { proxy } = getCurrentInstance();

	onLoad((options) => {
		console.log('options', options);
		state.orderId = options.orderId;
	});

	//勾选协议
	function onChange() {
		state.protocol = !state.protocol;
	}

	function handleClick() {
		if (!state.protocol) return handleTip();
	}

	async function wechatLogin(e) {
		const loginRes = await sheep.$platform.useProvider('wechat').login(e.detail);
		if (loginRes) {
			const { u, t } = sheep.$store('user').bindShareUser;
			console.log('u', u);
			console.log('t', t);
			if (u && t) {
				const shareRes = await sheep.$api.user.bindShareUser(u);
				if (shareRes.code == 200) {
					console.log(shareRes);
					sheep.$store('user').setBindShareUser({ u: '', t: '' });
					uni.showModal({
						title: '提示',
						content: '您已绑定成功',
						showCancel: false,
						confirmText: '返回首页',
						success: ({ confirm }) => {
							if (confirm) {
								sheep.$router.go('/pages/index/index');
							}
						},
					});
				}
			} else sheep.$helper.toast('登录成功');
			closeAuthModal();
		}
	}
	// 查看协议
	function onProtocol(id, title, value) {
		closeAuthModal();
		uni.setStorageSync('protocol', true);
		sheep.$router.go('/pages/public/richtext', {
			id,
			title,
			value,
		});
	}
	// 请先登录提示
	function handleTip() {
		// return uni.showToast({
		//   title: "请勾选我已阅读并同意《用户协议》！",
		//   icon: "none",
		//   mask: true,
		// });
		return uni.showModal({
			title: '微信提示',
			content: '阅读并同意《用户协议》',
			confirmText: '同意',
			cancelText: '不同意',
			success: ({ confirm }) => {
				if (confirm) {
					onChange();
				}
			},
		});
	}
</script>

<style lang="scss" scoped>

	.cancel-btn {
		width: 100%;
		height: 72rpx;
		font-weight: 500;
		font-size: 28rpx;

		border-radius: 40rpx;
		background: red;
		color: $white;
		margin-bottom: 10rpx;
	}

	.agreement-box {
		display: flex;

		.tcp-text {
			color: var(--ui-BG-Main);
		}
	}

	.btnStyle {
		border: none;
		font-size: 15px;
		padding: 0 !important;
		color: #82abcc;
		margin: 0;
		background-color: transparent;
		&::after {
			border: none;
		}
	}
	.agreement-text {
		justify-content: center;
		// position: absolute;
		// bottom: 0;
		width: calc(100% - 68rpx);
	}
	.buy-btn {
		width: 100%;
		height: 72rpx;
		font-weight: 500;
		font-size: 28rpx;

		border-radius: 40rpx;
		background: #0dd116;
		color: $white;
	}
	.auto-login-img {
		width: 48rpx;
		height: 48rpx;
		border-radius: 50%;
	}
</style>
