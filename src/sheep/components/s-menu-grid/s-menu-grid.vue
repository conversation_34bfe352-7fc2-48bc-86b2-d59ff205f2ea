<template>
	<view class="menu-grid-container">
		<view class="custom-grid" :class="`grid-col-${data.col || 3}`" v-if="menuItems.length > 0">
			<view class="grid-item" v-for="(item, index) in menuItems" :key="index" @tap="handleGo(item)">
				<view class="grid-content" v-if="!item.button">
					<view class="grid-card">
						<view class="icon-wrapper">
							<image class="menu-icon" :src="sheep.$url.static(item.src)" mode="aspectFit"></image>
							<view class="badge" v-if="item.badge.show" :style="{ background: item.badge.bgColor, color: item.badge.color }">
								{{ item.badge.text }}
							</view>
						</view>
						<view class="text-wrapper">
							<text class="title" :style="{ color: item.title.color }">{{ item.title.text }}</text>
							<text class="subtitle" v-if="item.tip.text" :style="{ color: item.tip.color }">{{ item.tip.text }}</text>
						</view>
					</view>
				</view>
				<view class="grid-content" v-else>
					<button open-type="contact" class="contact-btn">
						<view class="grid-card">
							<view class="icon-wrapper">
								<image class="menu-icon" :src="sheep.$url.static(item.src)" mode="aspectFit"></image>
								<view class="badge" v-if="item.badge.show" :style="{ background: item.badge.bgColor, color: item.badge.color }">
									{{ item.badge.text }}
								</view>
							</view>
							<view class="text-wrapper">
								<text class="title" :style="{ color: item.title.color }">{{ item.title.text }}</text>
								<text class="subtitle" v-if="item.tip.text" :style="{ color: item.tip.color }">{{ item.tip.text }}</text>
							</view>
						</view>
					</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import { computed } from 'vue';
	import sheep from '@/sheep';

	const userInfo = computed(() => sheep.$store('user').userInfo);

	const props = defineProps({
		data: {
			type: Object,
			default() {},
		},
	});

	// 处理菜单项显示逻辑
	const menuItems = computed(() => {
		if (!props.data.list) return [];

		return props.data.list
			.map((item) => {
				// 克隆对象以避免修改原始数据
				const newItem = { ...item };
				newItem.show = true;

				// 我的分享
				if (newItem.status === 'bind') {
					newItem.show = userInfo.value.isOrderTaker == 1;
				}

				// 我的接单员
				if (newItem.status === 'taker') {
					newItem.show = userInfo.value.isBindSpread == 1;
				}

				return newItem;
			})
			.filter((item) => item.show); // 过滤掉不显示的项
	});

	const handleGo = (item) => {
		if (!item.button) {
			sheep.$router.go(item.url);
		}
	};
</script>

<style lang="scss" scoped>
	.menu-grid-container {
		padding: 10rpx;
	}

	// 自定义网格布局 - 使用 CSS Grid
	.custom-grid {
		display: grid;
		width: 100%;
		gap: 0;
		box-sizing: border-box;

		// 动态列数支持
		&.grid-col-1 {
			grid-template-columns: 1fr;
		}

		&.grid-col-2 {
			grid-template-columns: repeat(2, 1fr);
		}

		&.grid-col-3 {
			grid-template-columns: repeat(3, 1fr);
		}

		&.grid-col-4 {
			grid-template-columns: repeat(4, 1fr);
		}

		&.grid-col-5 {
			grid-template-columns: repeat(5, 1fr);
		}

		&.grid-col-6 {
			grid-template-columns: repeat(6, 1fr);
		}
	}

	.grid-item {
		padding: 10rpx;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
	}

	.grid-content {
		flex: 1;
		display: flex;
		flex-direction: column;
		height: 100%;
	}

	.grid-card {
		display: flex;
		flex-direction: column;
		align-items: center;
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 20rpx 10rpx;
		// box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
		// transition: all 0.3s;

		&:active {
			transform: scale(0.96);
			background-color: var(--ui-BG-1);
		}
	}

	.icon-wrapper {
		position: relative;
		margin-bottom: 12rpx;
		width: 80rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 50%;
		background: linear-gradient(135deg, var(--ui-BG-Main-opacity-1), rgba(255, 255, 255, 0.8));
	}

	.menu-icon {
		width: 45rpx;
		height: 45rpx;
		z-index: 1;
	}

	.badge {
		position: absolute;
		top: -6rpx;
		right: -10rpx;
		font-size: 20rpx;
		padding: 2rpx 10rpx;
		border-radius: 20rpx;
		font-weight: bold;
		box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);
		transform: scale(0.8);
		z-index: 2;
	}

	.text-wrapper {
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 100%;
	}

	.title {
		font-size: 26rpx;
		font-weight: 500;
		text-align: center;
		margin-bottom: 4rpx;
		line-height: 1.4;
	}

	.subtitle {
		font-size: 20rpx;
		color: #999;
		text-align: center;
	}

	.contact-btn {
		padding: 0;
		margin: 0;
		line-height: normal;
		background: transparent;
		width: 100%;
		height: 100%;
		border: none;
		outline: none;

		&::after {
			border: none;
		}
	}
</style>
