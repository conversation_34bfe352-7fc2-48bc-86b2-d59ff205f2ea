<template>
		<movable-area class="movable-area">
			<movable-view class="movable-view" direction="all" :x="x" :y="y" @change="onChange" :out-of-bounds="false">
				<view class="ai-btn" @tap="handleTap()">
					<view class="ai-btn-inner">
						<image class="ai-icon" :src="sheep.$url.cdn('/2025/06/25be409bac6b594072aadddea2049b08ddai.png')" mode="widthFix" />
					</view>
				</view>
			</movable-view>
		</movable-area>
</template>

<script setup>
	import sheep from '@/sheep';
	import { ref, onMounted } from 'vue';

	// 初始位置 - 右下角
	const x = ref(0);
	const y = ref(0);

	// 在组件挂载后设置初始位置
	onMounted(() => {
		const sysInfo = uni.getSystemInfoSync();
		x.value = sysInfo.windowWidth - 80;
		y.value = sysInfo.windowHeight - 160;
		console.log('🚀 ~ setTimeout ~ sysInfo:', sysInfo);
		console.log('🚀 ~ setTimeout ~ x:', x.value);
		console.log('🚀 ~ setTimeout ~ y:', y.value);
	});

	// 位置变化事件
	const onChange = (e) => {
		// x.value = e.detail.x;
		// y.value = e.detail.y;
		
	};

	const emits = defineEmits(['open-ai-assistant']);

	// 点击事件
	const handleTap = () => {
		console.log('🚀 ~ handleTap ~ handleTap:', handleTap);
		emits('open-ai-assistant');
	};
</script>

<style lang="scss" scoped>
	.movable-area {
		width: 100%;
		height: 100%;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 9999;
		pointer-events: none;
	}

	.movable-view {
		width: 100rpx;
		height: 100rpx;
		z-index: 9999;
		pointer-events: auto;
	}

	.ai-btn {
		width: 100rpx;
		height: 100rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		.ai-btn-inner {
			position: relative;
			width: 100rpx;
			height: 100rpx;
			border-radius: 50%;
			background: #fff;
			box-shadow: var(--ui-Main-box-shadow);
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			transform-origin: center;
			transition: all 0.3s;
			overflow: hidden;

			&:active {
				transform: scale(0.92);
			}

			&::before {
				content: '';
				position: absolute;
				top: -10%;
				left: -10%;
				width: 120%;
				height: 120%;
				// background: linear-gradient(45deg, transparent 0%, rgba(255, 255, 255, 0.1) 45%, rgba(255, 255, 255, 0.3) 50%, rgba(255, 255, 255, 0.1) 55%, transparent 100%);
				transform: translateX(-100%) rotate(45deg);
				animation: shine 4s infinite;
			}
		}

		.ai-icon {
			width: 100%;
			height: 100%;
			position: absolute;
			top: 0;
			left: 0;
		}

		.ai-text {
			color: #fff;
			font-size: 42rpx;
			line-height: 1;
			font-weight: bold;
			letter-spacing: 2rpx;
			text-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.3);
			position: relative;
			z-index: 1;
		}

		.ai-pulse {
			position: absolute;
			width: 100%;
			height: 100%;
			border-radius: 50%;
			background: var(--ui-BG-Main-opacity-1);
			z-index: -1;
			animation: pulse 2s infinite;
		}
	}

	@keyframes pulse {
		0% {
			transform: scale(1);
			opacity: 0.8;
		}
		70% {
			transform: scale(1.3);
			opacity: 0;
		}
		100% {
			transform: scale(1.3);
			opacity: 0;
		}
	}

	@keyframes shine {
		0% {
			transform: translateX(-100%) rotate(45deg);
		}
		20%,
		100% {
			transform: translateX(100%) rotate(45deg);
		}
	}
</style>
