import request from '@/sheep/request';

export default {
  area: () =>
    request({
      url: '/h5/area',
      method: 'GET',
    }),
  faq: () =>
    request({
      url: 'data/faq',
      method: 'GET',
    }),
  richtext: (id) =>
    request({
      url: 'data/richtext/' + id,
      method: 'GET',
    }),
  richtextOther: (key) =>
    request({
      url: 'no-auth/getConfigByKey',
      method: 'GET',
      data: {
        key
      }
    }),
};
