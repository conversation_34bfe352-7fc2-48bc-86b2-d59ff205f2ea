import { aiUrl } from '@/sheep/config';
const aiAppKey = 'app-M1M8blRG5BZOvkfHQ4chgmK1';
export default {
    chatMessages: (data, onSuccess, onFail) => {
        return new Promise((resolve, reject) => {
            try {
                console.log('正在发起AI请求，URL:', aiUrl + '/v1/chat-messages');
                const requestTask = uni.request({
                    url: aiUrl + '/v1/chat-messages',
                    method: 'POST',
                    enableChunked: true,
                    // responseType: 'arraybuffer',
                    // enableHttp2: true,
                    header: {
                        'Authorization': 'Bearer ' + aiAppKey,
                        'Content-Type': 'application/json',
                        "Accept": 'text/event-stream',
                    },
                    data,
                    success: (res) => {
                        console.log('AI请求成功完成:', res);
                        onSuccess && onSuccess(res.data);
                        // 这里不需要resolve，因为我们需要返回requestTask对象
                    },
                    fail: (err) => {
                        console.error('AI请求失败:', err);
                        onFail && onFail(err);
                        reject(err);
                    }
                });

                console.log('AI请求对象已创建:', requestTask);
                // 直接返回requestTask对象，这样可以使用其onChunkReceived方法
                resolve(requestTask);
            } catch (error) {
                console.error('创建AI请求时发生异常:', error);
                reject(error);
            }
        });
    }
};