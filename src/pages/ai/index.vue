<template>
	<s-layout title="AI" navbar="normal">
		<view class="content">
			<!-- 聊天消息滚动容器，动态调整底部距离以适应键盘 -->
			<scroll-view class="chat-container" scroll-y scroll-with-animation :scroll-into-view="scrollToView" :style="{ bottom: getChatContainerBottom() }">
				<view class="message-list" id="message-list">
					<!-- 欢迎消息 -->
					<view class="message-item bot-message" id="welcome-msg">
						<view class="avatar">
							<!-- <image src="/static/kimi-avatar.png" mode="aspectFill"></image> -->
						</view>
						<view class="message-content">
							<view class="message-sender">Kimi</view>
							<view class="message-bubble">
								<text>Hi，我是 Kimi，很高兴遇见你！\n应用商店搜索 Kimi 随时和我聊天！</text>
							</view>
						</view>
					</view>

					<!-- 动态消息列表 -->
					<view v-for="(msg, index) in messages" :key="index" :id="'msg-' + index" :class="['message-item', msg.type === 'bot' ? 'bot-message' : 'user-message']">
						<!-- 机器人消息模板 -->
						<template v-if="msg.type === 'bot'">
							<view class="avatar">
								<!-- <image src="/static/kimi-avatar.png" mode="aspectFill"></image> -->
							</view>
							<view class="message-content">
								<view class="message-sender">Kimi</view>
								<view class="message-bubble">
									<view class="message-loading" v-if="msg.isLoading">
										<view class="loading-item"></view>
										<view class="loading-item"></view>
										<view class="loading-item"></view>
									</view>
									<view v-if="msg.isThinking" class="thinking-text">思考中...</view>
									<rich-text :nodes="msg.text" />
								</view>
							</view>
						</template>
						<!-- 用户消息模板 -->
						<template v-else>
							<view class="avatar user-avatar">
								<image src="/static/user-avatar.png" mode="aspectFill"></image>
							</view>
							<view class="message-content">
								<view class="message-sender">我</view>
								<view class="message-bubble user-bubble">
									<text>{{ msg.text }}</text>
								</view>
							</view>
						</template>
					</view>

					<!-- 底部留白，防止内容被输入框遮挡 -->
					<view class="bottom-space"></view>
				</view>
			</scroll-view>

			<!-- 输入区域，动态调整位置以适应键盘 -->
			<view class="input-section" :style="{ bottom: getInputSectionBottom() }">
				<!-- 录音时的遮罩层 -->
				<view class="mask-layer" v-if="isRecording"></view>

				<!-- 录音时的取消提示区域 - 暂时注释掉 -->
				<!-- <view class="cancel-area" v-if="isRecording && showCancelHint" :class="{ show: showCancelHint }">
				<view class="cancel-hint">
					<uni-icons type="close" size="24" color="#fff"></uni-icons>
					<text>松手取消</text>
				</view>
			</view> -->

				<!-- 输入容器，根据录音状态和键盘状态动态调整样式 -->
				<view class="input-container" :class="{ recording: isRecording }" :style="{ backgroundColor: getInputContainerColor() }">
					<!-- 输入模式切换按钮（语音/文字） -->
					<view class="mode-toggle" @tap="toggleInputMode" v-if="!isRecording">
						<div class="mode-toggle-inner">
							<uni-icons :type="isVoiceMode ? 'bars' : 'mic'" size="20" color="#333"></uni-icons>
						</div>
					</view>

					<!-- 文字输入容器 -->
					<view v-if="!isVoiceMode" class="text-input-container">
						<!-- 
						文字输入框
						cursor-spacing: 设置光标与键盘的距离
						adjust-position: 禁用自动调整，使用自定义调整逻辑
						hold-keyboard: 保持键盘显示状态
					-->
						<input class="text-input" v-model="inputText" placeholder="说点什么..." @confirm="sendMessage" @focus="handleInputFocus" @blur="handleInputBlur" confirm-type="send" cursor-spacing="10" :adjust-position="false" :hold-keyboard="true" />
					</view>
					<!-- 发送按钮，仅在有文字内容且非语音模式时显示 -->
					<view class="send-btn" @tap="sendMessage" v-if="inputText.trim() && !isVoiceMode">
						<uni-icons v-if="!isFinished" type="paperplane-filled" size="18" color="#fff"></uni-icons>
						<uni-icons v-if="isFinished" type="minus-filled" size="18" color="#fff"></uni-icons>
					</view>
					<!-- 语音输入按钮，支持触摸事件处理 -->
					<view v-if="isVoiceMode" class="voice-input-btn" :class="{ recording: isRecording }" @touchstart.prevent="startRecord" @touchmove.prevent="onTouchMove" @touchend.prevent="stopRecord" @touchcancel.prevent="cancelRecord">
						<text v-if="!isRecording && !isProcessing">按住 说话</text>
						<text v-if="isRecording && !showCancelHint">松手发送 上滑取消</text>
						<text v-if="isRecording && showCancelHint">松手 取消</text>
						<text v-if="isProcessing"> 正在识别语音...</text>
					</view>
					<!-- 录音时的语音波纹动画 -->
					<view class="voice-wave" v-if="isRecording">
						<view class="wave-item" v-for="(item, index) in 24" :key="index" :style="{ height: waveSizes[index % waveSizes.length] + 'rpx' }"></view>
					</view>
				</view>
			</view>
		</view>
	</s-layout>
</template>

<script setup>
	import sheep from '@/sheep';
	import { ref, reactive, nextTick, onMounted, onBeforeUnmount } from 'vue';
	import { onLoad } from '@dcloudio/uni-app';

	import { ChunkRes } from '@/sheep/hooks/chunkRes';

	// AI回复相关变量
	const isAiResponding = ref(false); // 是否正在AI回复
	const currentAiMessage = ref(''); // 当前AI回复内容
	const currentAiAudio = ref(''); // 当前AI回复音频
	const aiChunkRes = ChunkRes(); // 流式响应处理
	const isLoadingComplete = ref(false); // 添加加载完成标志
	const isThinking = ref(false); // 是否正在思考
	const isLoading = ref(false); // 是否正在加载
	const isFinished = ref(false); // 是否完成
	onLoad(async (options) => {
		// 初始化页面时不需要发送自动请求，移除原有测试代码
	});

	// 引入微信小程序语音识别插件
	const plugin = requirePlugin('WechatSI');
	const manager = plugin.getRecordRecognitionManager();

	// 输入模式控制
	const isVoiceMode = ref(true); // 是否为语音输入模式
	const isRecording = ref(false); // 是否正在录音
	const inputText = ref(''); // 文字输入内容

	// 消息数据
	const messages = ref([]); // 聊天消息列表
	const scrollToView = ref('welcome-msg'); // 滚动到指定消息的ID

	// 触摸交互相关
	let touchStartY = 0; // 触摸开始时的Y坐标
	const showCancelHint = ref(false); // 是否显示取消提示
	const cancelThreshold = 50; // 上滑取消的阈值（单位：像素）
	let buttonRect = null; // 语音按钮的位置信息

	// 语音波纹动画相关
	const waveSizes = ref([30, 45, 38, 50]); // 语音波纹高度数组
	const waveColors = ['#ff6b6b', '#48dbfb', '#1dd1a1', '#feca57', '#ff9ff3']; // 语音波纹颜色数组
	let waveAnimationTimer = null; // 波纹动画定时器

	// 录音状态控制
	let recordingTimeout = null; // 录音超时定时器
	const isProcessing = ref(false); // 是否正在处理录音结果

	// 键盘适配相关
	const keyboardHeight = ref(0); // 键盘高度（px）
	const isKeyboardShow = ref(false); // 键盘是否显示

	/**
	 * 切换输入模式（语音/文字）
	 */
	function toggleInputMode() {
		isVoiceMode.value = !isVoiceMode.value;
	}

	/**
	 * 初始化语音识别功能
	 * 设置语音识别的各种回调事件
	 */
	function initRecognition() {
		// 检查插件是否存在
		if (typeof requirePlugin !== 'function') {
			console.error('插件加载失败，请确保在微信小程序环境中运行');
			return;
		}

		try {
			// 录音停止回调
			manager.onStop = (res) => {
				console.log('🚀 ~ onStop ~ 开始处理录音结果');
				isProcessing.value = true;
				clearRecordingTimeout();

				// 延迟一下再处理，确保状态更新
				setTimeout(() => {
					isRecording.value = false;
					stopWaveAnimation();

					console.log('🚀 ~ initRecognition ~ showCancelHint:', showCancelHint.value);
					console.log('🚀 ~ initRecognition ~ res.result:', res.result);

					if (showCancelHint.value) {
						// 用户主动取消
						uni.showToast({
							title: '已取消录音',
							icon: 'none',
						});
					} else if (res.result && res.result.trim()) {
						// 有识别结果，发送到AI
						addUserMessage(res.result);
						sendToAI(res.result); // 使用真实AI而非模拟响应
					} else {
						// 没有识别到内容
						uni.showToast({
							title: '未识别到语音内容，请重试',
							icon: 'none',
						});
					}

					// 重置所有状态
					resetRecordingState();
				}, 100);
			};

			// 录音开始回调
			manager.onStart = () => {
				console.log('开始录音识别');
				isProcessing.value = false;
				startWaveAnimation();
				setRecordingTimeout();
			};

			// 录音错误回调
			manager.onError = (res) => {
				console.log('🚀 ~ onError ~ 录音识别错误:', res);
				clearRecordingTimeout();
				resetRecordingState();
				uni.showToast({
					title: '录音识别错误，请重试',
					icon: 'none',
				});
			};
		} catch (e) {
			console.error('语音识别插件初始化失败', e);
		}
	}

	/**
	 * 开始录音
	 * @param {Event} e - 触摸开始事件
	 */
	function startRecord(e) {
		console.log('🚀 ~ startRecord ~ isRecording:', isRecording.value);
		if (isRecording.value || isProcessing.value) return;

		isRecording.value = true;
		touchStartY = e.touches[0].clientY;
		showCancelHint.value = false; // 重置取消状态

		if (manager) {
			try {
				manager.start({
					duration: 30000, // 最大录音时长30秒
					lang: 'zh_CN', // 中文识别
				});

				// 有震动API的设备上添加短震动反馈
				if (uni.vibrateShort) {
					uni.vibrateShort();
				}
			} catch (error) {
				console.error('启动录音失败:', error);
				resetRecordingState();
				isRecording.value = false;
				uni.showToast({
					title: '录音启动失败，请重试',
					icon: 'none',
				});
			}
		} else {
			uni.showToast({
				title: '语音识别插件未初始化',
				icon: 'none',
			});
			isRecording.value = false;
		}
	}

	/**
	 * 获取语音按钮的位置信息
	 * 用于判断触摸点是否在按钮区域内
	 */
	function getButtonRect() {
		// 在Composition API中不能使用this，直接使用createSelectorQuery
		const query = uni.createSelectorQuery();
		query
			.select('.voice-input-btn')
			.boundingClientRect((data) => {
				buttonRect = data;
			})
			.exec();
	}

	/**
	 * 处理触摸移动事件
	 * 判断是否需要显示取消提示
	 * @param {Event} e - 触摸移动事件
	 */
	function onTouchMove(e) {
		console.log('🚀 ~ onTouchMove ~ e:', e);
		if (!isRecording.value) return;

		try {
			// 获取触摸点位置
			const touch = e.touches[0];

			// 计算上滑距离
			const moveDistance = touchStartY - touch.clientY;
			const isUpwardSlide = moveDistance > cancelThreshold; // 大于阈值视为向上滑动
			console.log('🚀 ~ onTouchMove ~ isUpwardSlide:', isUpwardSlide);

			// 判断触摸点是否在按钮区域内
			const isInButton = buttonRect && touch.clientX >= buttonRect.left && touch.clientX <= buttonRect.right && touch.clientY >= buttonRect.top && touch.clientY <= buttonRect.bottom;
			console.log('🚀 ~ onTouchMove ~ isInButton:', isInButton);

			// 如果向上滑动或离开按钮区域，都显示取消提示
			if (isUpwardSlide || !isInButton) {
				if (!showCancelHint.value) {
					showCancelHint.value = true;
					// 添加震动反馈
					if (uni.vibrateShort) {
						uni.vibrateShort();
					}
				}
			} else {
				if (showCancelHint.value) {
					showCancelHint.value = false;
					// 添加震动反馈
					if (uni.vibrateShort) {
						uni.vibrateShort();
					}
				}
				console.log('我回来了');
			}
		} catch (e) {
			console.log('🚀 ~ onTouchMove ~ e:', e);
			isRecording.value = false;
			showCancelHint.value = false;
		}
	}

	/**
	 * 停止录音
	 * 处理录音结束逻辑
	 */
	function stopRecord() {
		console.log('🚀 ~ stopRecord ~ isRecording:', isRecording.value);
		if (!isRecording.value) return;

		console.log('🚀 ~ stopRecord ~ manager:', manager);
		if (manager) {
			try {
				// 设置处理状态，防止重复操作
				isProcessing.value = true;
				manager.stop();
			} catch (e) {
				console.log('🚀 ~ stopRecord ~ error:', e);
				// 如果stop方法出错，重置状态
				clearRecordingTimeout();
				resetRecordingState();
				isRecording.value = false;
				stopWaveAnimation();
				uni.showToast({
					title: '录音停止失败，请重试',
					icon: 'none',
				});
			}
		} else {
			// 如果manager不存在，直接重置状态
			resetRecordingState();
			isRecording.value = false;
			stopWaveAnimation();
		}
	}

	/**
	 * 取消录音
	 * 用户主动取消录音操作
	 */
	function cancelRecord() {
		console.log('🚀 ~ cancelRecord ~ isRecording:', isRecording.value);
		if (!isRecording.value) return;

		showCancelHint.value = true;
		clearRecordingTimeout();

		if (manager) {
			try {
				isProcessing.value = true;
				manager.stop();
			} catch (error) {
				console.error('取消录音失败:', error);
				// 直接重置状态
				resetRecordingState();
				isRecording.value = false;
				stopWaveAnimation();
				uni.showToast({
					title: '已取消录音',
					icon: 'none',
				});
			}
		} else {
			// 如果manager不存在，直接重置状态
			resetRecordingState();
			isRecording.value = false;
			stopWaveAnimation();
			uni.showToast({
				title: '已取消录音',
				icon: 'none',
			});
		}
	}

	/**
	 * 开始语音波纹动画
	 * 模拟语音输入时的视觉效果
	 */
	function startWaveAnimation() {
		// 随机改变波形大小，模拟语音波动效果
		waveAnimationTimer = setInterval(() => {
			waveSizes.value = [Math.floor(Math.random() * 25) + 25, Math.floor(Math.random() * 30) + 30, Math.floor(Math.random() * 25) + 25, Math.floor(Math.random() * 35) + 25];
		}, 150);
	}

	/**
	 * 停止语音波纹动画
	 */
	function stopWaveAnimation() {
		if (waveAnimationTimer) {
			clearInterval(waveAnimationTimer);
			waveAnimationTimer = null;
		}
	}

	/**
	 * 发送文字消息
	 */
	function sendMessage() {
		if (!inputText.value.trim()) return;
		uni.hideKeyboard();
		addUserMessage(inputText.value);
		sendToAI(inputText.value);
		inputText.value = '';
	}

	/**
	 * 发送消息到AI并处理响应
	 * @param {string} userInput - 用户输入内容
	 */
	async function sendToAI(userInput) {
		try {
			isAiResponding.value = true;
			currentAiMessage.value = '';
			currentAiAudio.value = '';
			isLoadingComplete.value = false; // 重置加载完成标志
			isFinished.value = true; // 重置完成标志

			// 显示"正在输入"的消息占位
			messages.value.push({
				type: 'bot',
				text: '...',
				audio: '',
				isTyping: true,
				isLoading: true,
				isThinking: false,
			});

			scrollToBottom();

			console.log('开始请求AI接口，参数:', {
				query: userInput,
				user: 'user-' + Date.now(),
			});

			// 先尝试处理API请求
			let apiResponse;
			try {
				apiResponse = await sheep.$api.ai.chatMessages(
					{
						inputs: {
						},
						query: userInput,
						response_mode: 'streaming',
						user: 'user-' + Date.now(), // 使用时间戳生成唯一用户ID
					},
					(resultData) => {
						if (resultData) {
							try {
								console.log('🚀 ~ sendToAI ~ resultData:', resultData.split('\n'));
								// 确保resultData是字符串
								if (typeof resultData !== 'string') {
									console.error('收到的resultData不是字符串类型:', typeof resultData);
									resultData = String(resultData); // 尝试转换为字符串
								}

								const texts = resultData.split('\n');
								if (texts && texts.length > 0) {
									const resTexts = texts.filter((item) => item !== '');
									const data = resTexts.map((item) => {
										// 去掉开头的data:
										return item.replace(/^data:/, '');
									});
									processResponseTexts(data, messageIndex);
								}
							} catch (error) {
								console.error('处理AI响应数据出错:', error);
								// 显示错误消息给用户
								if (messages.value[messageIndex]) {
									messages.value[messageIndex].isLoading = false;
									messages.value[messageIndex].isThinking = false;
									messages.value[messageIndex].text = '抱歉，我处理响应时遇到问题，请重试。';
								}
							}
						} else {
							console.warn('接收到空的AI响应数据');
							if (messages.value[messageIndex]) {
								messages.value[messageIndex].isLoading = false;
							}
						}
					}
				);

				console.log('AI接口请求成功，返回对象:', apiResponse);
			} catch (apiError) {
				console.error('AI接口请求失败:', apiError);
				isLoading.value = false; // 发生错误时隐藏加载状态
				throw apiError;
			}

			// 移除"正在输入"的占位消息
			messages.value = messages.value.filter((msg) => !msg.isTyping);

			// 添加空消息，准备接收流式响应
			addBotMessage({ isLoading: true, isThinking: false });
			const messageIndex = messages.value.length - 1;

			console.error('API返回对象没有onChunkReceived方法:', apiResponse.onChunkReceived);
			// 确认apiResponse有onChunkReceived方法
			if (typeof apiResponse.onChunkReceived !== 'function') {
				addBotMessage({ text: '抱歉，无法接收AI响应数据，请稍后再试。' });
				isLoading.value = false; // 出错时隐藏加载状态
				return;
			}

			// 设置chunk接收处理器
			apiResponse.onChunkReceived((chunk) => {
				console.log('收到chunk数据:', chunk);

				// 检查chunk.data是否存在
				if (!chunk || !chunk.data) {
					console.error('收到无效的chunk数据:', chunk);
					return;
				}

				// 将完整的buffer数据分片处理
				const bufferData = chunk.data;

				// 如果不是二进制数据，直接处理
				const resTexts = aiChunkRes.onChunkReceivedReturn(bufferData);
				console.log('解析后的resTexts:', resTexts);

				// 如果有解析结果，处理每条消息
				if (resTexts && resTexts.length > 0) {
					processResponseTexts(resTexts, messageIndex);
				}
			});

			// 监听请求完成事件
			if (apiResponse.onHeadersReceived) {
				apiResponse.onHeadersReceived((res) => {
					console.log('收到响应头:', res);
				});
			}

			// 使用setTimeout模拟检测流结束，如果5秒后未收到done标记，则认为已完成
			// setTimeout(() => {
			// 	if (!isLoadingComplete.value) {
			// 		isLoadingComplete.value = true;
			// 		console.log('通过超时检测判断AI回复已完成');
			// 		if (messages.value[messageIndex]) {
			// 			messages.value[messageIndex].text = currentAiMessage.value;
			// 		}
			// 	}
			// }, 5000);
		} catch (error) {
			console.error('AI请求失败:', error);
			// 移除"正在输入"的占位消息
			messages.value = messages.value.filter((msg) => !msg.isTyping);
			// 显示错误消息
			addBotMessage({ text: '抱歉，我暂时无法回应，请稍后再试。' });
		} finally {
			// isAiResponding 状态不要太早结束，等到真正接收完毕再结束
			setTimeout(() => {
				isAiResponding.value = false;
			}, 1000);
		}
	}

	// 新增一个函数来处理响应文本，避免重复代码
	function processResponseTexts(resTexts, messageIndex) {
		if (!Array.isArray(resTexts) || !messages.value[messageIndex]) {
			console.error('无效的参数:', { resTexts, messageIndex });
			return;
		}

		resTexts.forEach((text) => {
			try {
				console.log('处理单个text:', text);
				// 检查text是否为空
				if (!text || typeof text !== 'string') {
					console.warn('跳过无效的响应文本:', text);
					return;
				}

				let data;
				try {
					data = JSON.parse(text);
				} catch (parseError) {
					console.error('JSON解析失败:', parseError, '原始文本:', text);
					return; // 跳过此条无效数据
				}

				console.log('解析后的JSON数据:', data);

				// 根据事件类型进行处理
				if (data.event === 'message' || data.event === 'agent_message') {
					// 处理AI消息返回
					if (data.answer) {
						messages.value[messageIndex].isThinking = false; // 收到回复时隐藏思考状态
						messages.value[messageIndex].isLoading = false; // 收到回复时隐藏加载状态
						// 更新当前消息内容
						currentAiMessage.value += data.answer;
						// 直接更新消息列表中的最后一条消息
						messages.value[messageIndex].text = currentAiMessage.value + (isLoadingComplete.value ? '' : '...');
						scrollToBottom();
					}
				} else if (data.event === 'message_end') {
					// 消息结束事件
					isLoadingComplete.value = true;
					isFinished.value = false; // 回复结束时设置完成标志
					messages.value[messageIndex].text = currentAiMessage.value;
					messages.value[messageIndex].isLoading = false; // 回复结束时隐藏加载状态
					messages.value[messageIndex].isThinking = false; // 回复结束时隐藏思考状态
					console.log('AI回复完成，消息ID:', data.message_id);
				} else if (data.event === 'agent_thought') {
					// Agent思考过程，在界面上显示
					console.log('Agent思考:', data.thought);
					if (data.thought) {
						// 构建思考内容的HTML格式
						messages.value[messageIndex].isThinking = true; // 显示思考状态
						messages.value[messageIndex].isLoading = false; // 思考时隐藏加载状态
						// 更新当前消息内容
						currentAiMessage.value += data.thought;
						// 将思考内容添加到当前消息
						messages.value[messageIndex].text = currentAiMessage.value + (isLoadingComplete.value ? '' : '...');
						scrollToBottom();
						// 如果有工具使用信息，也可以显示
						if (data.tool) {
							console.log('使用的工具:', data.tool);
						}
					}
				} else if (data.event === 'message_file') {
					// 文件事件，目前主要是图片
					console.log('收到文件:', data);
					// 如果是图片，可以将其添加到消息中
					if (data.type === 'image') {
						const imageHtml = `<img src="${data.url}" style="max-width: 100%; border-radius: 8rpx;" />`;
						currentAiMessage.value += imageHtml;
						messages.value[messageIndex].text = currentAiMessage.value + (isLoadingComplete.value ? '' : '...');
						scrollToBottom();
					}
					messages.value[messageIndex].isThinking = false;
					messages.value[messageIndex].isLoading = false; // 收到文件时隐藏加载状态
				} else if (data.event === 'tts_message') {
					if (data.audio) {
						currentAiAudio.value += data.audio;
						messages.value[messageIndex].audio = currentAiAudio.value + (isLoadingComplete.value ? '' : '...');
					}
				} else {
					console.warn('未识别的数据格式:', data);
				}
			} catch (e) {
				console.error('解析AI响应数据失败:', e, '原始文本:', text);
				isFinished.value = false; // 回复结束时设置完成标志
				messages.value[messageIndex].isLoading = false; // 回复结束时隐藏加载状态
				messages.value[messageIndex].isThinking = false; // 回复结束时隐藏思考状态
			}
		});
	}

	/**
	 * 添加用户消息到聊天列表
	 * @param {string} text - 消息内容
	 */
	function addUserMessage(text) {
		messages.value.push({
			type: 'user',
			text: text,
		});
		scrollToBottom();
	}

	/**
	 * 添加机器人消息到聊天列表
	 * @param {string} text - 消息内容
	 */
	function addBotMessage(options = {}) {
		const { text = '', isLoading = false, isThinking = false, audio = '' } = options;
		messages.value.push({
			type: 'bot',
			text: text,
			audio: audio,
			isLoading: isLoading, // 如果未加载完成且有内容，则显示加载状态
			isThinking: isThinking,
		});
		scrollToBottom();
	}

	/**
	 * 滚动到聊天底部
	 * 显示最新消息
	 */
	function scrollToBottom() {
		nextTick(() => {
			scrollToView.value = messages.value.length > 0 ? 'msg-' + (messages.value.length - 1) : 'welcome-msg';
		});
	}

	/**
	 * 获取输入容器的背景颜色
	 * 根据不同状态返回不同颜色
	 * @returns {string} 颜色值
	 */
	function getInputContainerColor() {
		if (isProcessing.value) {
			return '#feca57'; // 处理中时黄色
		}
		if (isRecording.value) {
			return showCancelHint.value ? '#ff6b6b' : '#4080ff'; // 取消时红色，录音时蓝色
		}
		return '#ffffff'; // 默认白色
	}

	/**
	 * 清除录音超时定时器
	 */
	function clearRecordingTimeout() {
		if (recordingTimeout) {
			clearTimeout(recordingTimeout);
			recordingTimeout = null;
		}
	}

	/**
	 * 设置录音超时定时器
	 * 防止录音时间过长
	 */
	function setRecordingTimeout() {
		clearRecordingTimeout(); // 先清除之前的定时器
		recordingTimeout = setTimeout(() => {
			console.log('录音超时，强制停止');
			if (manager) {
				try {
					manager.stop();
				} catch (error) {
					console.error('超时停止录音失败:', error);
				}
			}
			isRecording.value = false;
			stopWaveAnimation();
			resetRecordingState();
			uni.showToast({
				title: '录音超时，已自动停止',
				icon: 'none',
			});
		}, 30000); // 30秒超时
	}

	/**
	 * 重置录音相关状态
	 */
	function resetRecordingState() {
		showCancelHint.value = false;
		isProcessing.value = false;
		isRecording.value = false;
		clearRecordingTimeout();
		stopWaveAnimation();
	}

	/**
	 * 处理输入框聚焦事件
	 * 键盘弹起时的处理逻辑
	 */
	function handleInputFocus() {
		// 处理输入框聚焦时的逻辑
		isKeyboardShow.value = true;
		// 滚动到最新消息
		nextTick(() => {
			scrollToBottom();
		});
	}

	/**
	 * 处理输入框失焦事件
	 * 键盘收起时的处理逻辑
	 */
	function handleInputBlur() {
		// 处理输入框失去焦点时的逻辑
		isKeyboardShow.value = false;
		keyboardHeight.value = 0;
	}

	/**
	 * 初始化键盘高度监听
	 * 解决输入框被键盘遮挡的问题
	 */
	function initKeyboardListener() {
		// 监听键盘高度变化
		uni.onKeyboardHeightChange((res) => {
			console.log('键盘高度变化:', res.height);
			keyboardHeight.value = res.height;
			isKeyboardShow.value = res.height > 0;

			// 当键盘弹起时，滚动到底部
			if (res.height > 0) {
				nextTick(() => {
					scrollToBottom();
				});
			}
		});
	}

	/**
	 * 计算聊天容器的底部距离
	 * 根据键盘状态动态调整
	 * @returns {string} CSS样式值
	 */
	function getChatContainerBottom() {
		// 输入框高度约为160rpx，转换为px大约80px
		const inputHeight = 80;
		if (isKeyboardShow.value && keyboardHeight.value > 0) {
			return `${keyboardHeight.value + inputHeight}px`;
		}
		return `${inputHeight}px`;
	}

	/**
	 * 计算输入区域的底部距离
	 * 根据键盘状态动态调整位置
	 * @returns {string} CSS样式值
	 */
	function getInputSectionBottom() {
		if (isKeyboardShow.value && keyboardHeight.value > 0) {
			// 键盘弹起时，输入框跟随键盘位置，并增加10px间距
			return `${keyboardHeight.value + 10}px`;
		}
		// 键盘收起时，使用安全区域距离
		return 'max(30px, env(safe-area-inset-bottom) + 10px)';
	}

	// 生命周期钩子
	onMounted(() => {
		// 初始化语音识别功能
		initRecognition();
		// 获取语音按钮的位置信息，用于触摸交互判断
		getButtonRect();
		// 初始化键盘高度监听，解决输入框被遮挡问题
		initKeyboardListener();

		// 延迟显示默认欢迎消息（可以保留这条默认消息）
		setTimeout(() => {
			addBotMessage({ text: '嗨！我是Kimi，有什么我可以帮助你的吗？' });
		}, 500);
	});

	onBeforeUnmount(() => {
		// 清理录音相关的定时器和状态
		clearRecordingTimeout();
		stopWaveAnimation();
		resetRecordingState();
		isRecording.value = false;
		// 清理键盘监听，避免内存泄漏
		uni.offKeyboardHeightChange();
	});
</script>

<style lang="scss" scoped>
	/* 主容器样式 */
	.content {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f9f9fb; /* 更柔和的背景色 */
		position: relative;
	}

	/* 聊天消息滚动容器 */
	.chat-container {
		flex: 1;
		padding: 20rpx;
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		box-sizing: border-box;
		/* bottom值通过动态样式控制，适应键盘状态 */
	}

	/* 消息列表容器 */
	.message-list {
		padding-bottom: 40rpx;
	}

	/* 单条消息项 */
	.message-item {
		margin-bottom: 40rpx;
		display: flex;
		position: relative;
	}

	/* 底部留白，防止内容被输入框遮挡 */
	.bottom-space {
		height: 40rpx;
	}

	/* 机器人消息布局 */
	.bot-message {
		align-items: flex-start;
	}

	/* 用户消息布局（右对齐） */
	.user-message {
		flex-direction: row-reverse;
		align-items: flex-start;
	}

	/* 头像样式 */
	.avatar {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		overflow: hidden;
		background-color: #e0e0e0;
		margin-right: 20rpx;
	}

	.avatar image {
		width: 100%;
		height: 100%;
	}

	/* 消息内容容器 */
	.message-content {
		flex: 1;
		max-width: 70%;
	}

	/* 发送者名称 */
	.message-sender {
		font-size: 26rpx;
		color: #666;
		margin-bottom: 8rpx;
	}

	/* 消息气泡 */
	.message-bubble {
		padding: 20rpx;
		border-radius: 16rpx;
		background-color: #ffffff;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);

		.thinking-text {
			font-size: 26rpx;
			color: #666;
			margin-bottom: 8rpx;
		}
	}

	/* 用户消息内容容器 */
	.user-content {
		display: flex;
		justify-content: flex-end;
	}

	/* 用户消息气泡 */
	.user-bubble {
		background-color: #4080ff;
		color: #ffffff;
	}

	/* 消息操作按钮 */
	.message-actions {
		display: flex;
		margin-top: 10rpx;
	}

	.action-btn {
		padding: 10rpx 20rpx;
		font-size: 28rpx;
		color: #666;
	}

	/* 输入区域容器 */
	.input-section {
		position: fixed;
		left: 0;
		right: 0;
		z-index: 100;
		padding: 0 40rpx;
		/* bottom值通过动态样式控制，适应键盘状态 */
	}

	/* 输入框容器 */
	.input-container {
		display: flex;
		align-items: center;
		border-radius: 999rpx;
		padding: 20rpx 30rpx;
		box-shadow: 0 16rpx 40rpx rgba(0, 0, 0, 0.2);
		margin: 0 auto;
		transition: all 0.2s ease;
		position: relative;
		z-index: 100;
		background-color: #ffffff;
	}

	/* 录音状态下的输入容器样式 */
	.input-container.recording {
		transition: background 0.2s;
		padding-right: 0;
	}

	/* 语音输入按钮 */
	.voice-input-btn {
		flex: 1;
		height: 60rpx;
		line-height: 60rpx;
		text-align: center;
		font-size: 28rpx;
		color: #333;
		font-weight: bold;
		background: transparent;
		border: none;
		outline: none;
		margin-right: 40rpx;
	}

	/* 录音状态下的语音按钮文字颜色 */
	.input-container.recording .voice-input-btn {
		color: #fff;
	}

	/* 输入模式切换按钮 */
	.mode-toggle {
		.mode-toggle-inner {
			width: 60rpx;
			height: 60rpx;
			border-radius: 50%;
			border: 4rpx solid #333;
			display: flex;
			align-items: center;
			justify-content: center;
			box-sizing: border-box;
		}
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #333;
	}

	/* 文字输入容器 */
	.text-input-container {
		flex: 1;
		margin: 0 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	/* 文字输入框 */
	.text-input {
		height: 60rpx;
		font-size: 28rpx;
		color: #333;
		flex: 1;
	}

	/* 发送按钮 */
	.send-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 50%;
		background: linear-gradient(135deg, #4e8cff, #3b7dff);
		color: #ffffff;
		box-shadow: 0 4rpx 12rpx rgba(64, 128, 255, 0.4);
		transition: all 0.2s;
	}

	/* 发送按钮按下效果 */
	.send-btn:active {
		transform: scale(0.95);
		box-shadow: 0 2rpx 6rpx rgba(64, 128, 255, 0.3);
	}

	/* 取消录音提示区域 */
	.cancel-area {
		position: absolute;
		left: 50%;
		bottom: 180rpx; /* 增加与底部的距离 */
		transform: translateX(-50%) translateY(20rpx);
		opacity: 0;
		pointer-events: none;
		min-width: 180rpx;
		max-width: 80vw;
		padding: 18rpx 36rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background: linear-gradient(135deg, rgba(255, 107, 107, 0.92), rgba(255, 71, 87, 0.92));
		border-radius: 32rpx;
		box-shadow: 0 8rpx 32rpx rgba(255, 71, 87, 0.28);
		z-index: 200; /* 增加z-index确保在其他元素上方 */
		transition: opacity 0.25s, transform 0.25s;
		backdrop-filter: blur(6px);
		-webkit-backdrop-filter: blur(6px);
		border: 1px solid rgba(255, 255, 255, 0.18);
	}

	/* 显示取消提示时的样式 */
	.cancel-area.show {
		opacity: 1;
		transform: translateX(-50%) translateY(0);
		pointer-events: auto;
	}

	/* 取消提示内容 */
	.cancel-hint {
		display: flex;
		flex-direction: row;
		align-items: center;
		color: #fff;
		gap: 12rpx;
		font-size: 28rpx;
		font-weight: bold;
		letter-spacing: 1px;
	}

	/* 语音波纹容器 */
	.voice-wave {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 10rpx;
		height: 80rpx;
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
		top: -120rpx; /* 稍微上移一点 */
		z-index: 150;
	}

	/* 单个波纹条 */
	.wave-item {
		width: 12rpx;
		background-color: #ff6b6b;
		border-radius: 6rpx;
		transition: height 0.15s ease-in-out;
	}

	/* 多种颜色的波形样式 */
	.wave-item:nth-child(5n + 1) {
		background-color: #ff6b6b;
	}

	.wave-item:nth-child(5n + 2) {
		background-color: #48dbfb;
	}

	.wave-item:nth-child(5n + 3) {
		background-color: #1dd1a1;
	}

	.wave-item:nth-child(5n + 4) {
		background-color: #feca57;
	}

	.wave-item:nth-child(5n + 5) {
		background-color: #ff9ff3;
	}

	/* 用户头像样式 */
	.user-avatar {
		margin-right: 0;
		margin-left: 20rpx;
	}

	/* 用户消息发送者名称右对齐 */
	.user-message .message-sender {
		text-align: right;
	}

	/* 用户消息气泡样式 */
	.user-message .message-bubble {
		background-color: #4080ff;
		color: #ffffff;
	}

	/* 消息加载动画 */
	.message-loading {
		display: flex;
		align-items: center;
		justify-content: flex-start;
		gap: 8rpx;
		padding: 10rpx 0;
	}

	.loading-item {
		width: 12rpx;
		height: 12rpx;
		border-radius: 50%;
		background-color: #4080ff;
		opacity: 0.6;
		animation: bounce 1.4s infinite ease-in-out both;
	}

	.loading-item:nth-child(1) {
		animation-delay: -0.32s;
	}

	.loading-item:nth-child(2) {
		animation-delay: -0.16s;
	}

	/* 加载动画 */
	@keyframes bounce {
		0%,
		80%,
		100% {
			transform: scale(0);
		}
		40% {
			transform: scale(1);
		}
	}

	/* 思考样式 */
	.thinking-text {
		display: flex;
		align-items: center;
		font-style: italic;
	}

	.thinking-text::before {
		content: '';
		display: inline-block;
		width: 12rpx;
		height: 12rpx;
		border-radius: 50%;
		background-color: #4080ff;
		margin-right: 10rpx;
		animation: pulsate 1.5s infinite ease-in-out;
	}

	@keyframes pulsate {
		0%,
		100% {
			transform: scale(0.8);
			opacity: 0.5;
		}
		50% {
			transform: scale(1.2);
			opacity: 1;
		}
	}

	/* 录音时的遮罩层 */
	.mask-layer {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 99;
		pointer-events: none; /* 允许触摸事件穿透 */
	}

	/* 禁用语音按钮文字的指针事件，确保触摸事件正确传递 */
	.voice-input-btn text {
		pointer-events: none;
	}

	.loading-text {
		position: absolute;
		height: 100%;
		width: 100%;
		z-index: 100;
		color: #fff;
		font-size: 28rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
</style>
