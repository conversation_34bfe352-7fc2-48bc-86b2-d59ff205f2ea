<!-- 售后列表 -->
<template>
	<s-layout title="售后列表">
		<!-- tab -->
		<su-sticky bgColor="#fff">
			<su-tabs :list="tabMaps" :scrollable="false" @change="onTabsChange" :current="state.currentTab"></su-tabs>
		</su-sticky>
		<s-empty v-if="state.pagination.total === 0" icon="/static/data-empty.png" text="暂无数据"> </s-empty>
		<!-- 列表 -->
		<view v-if="state.pagination.total > 0" class="aftersale-list-container">
			<view class="aftersale-card" v-for="order in state.pagination.data" :key="order.id" @tap="handleDetail(order)">
				<view class="card-header">
					<view class="order-info">
						<view class="service-number">
							<text class="label">售后单号：</text>
							<text class="number">{{ order.id }}</text>
						</view>
					</view>
					<view class="status-wrapper">
						<text class="status-text" :class="getStatusClass(order.aftersaleStatus)">{{ getStatusText(order.aftersaleStatus) }}</text>
					</view>
				</view>

				<view class="goods-section">
					<view v-for="item in order.productList" :key="item.id" class="goods-item-wrapper">
						<s-goods-item :img="item.pic" :title="item.productName" :skuText="getSpDataValue(item.spData)" :price="item.salePrice" :num="item.buyNum"></s-goods-item>
					</view>
				</view>

				<view class="apply-info-section">
					<view class="apply-content">
						<view class="apply-type">{{ getRefundTypeText(order.applyRefundType) }}</view>
						<view class="apply-reason">{{ order.reason }}</view>
					</view>
					<view class="arrow-icon">
						<text class="cicon-forward"></text>
					</view>
				</view>
				<view class="tool-btn-box ss-flex ss-col-center ss-row-right ss-flex-gap-20">
					<view>
						<button class="ss-reset-button apply-btn" @tap.stop="onApply(order.orderId)" v-if="order.btns.includes('cancel')">取消申请</button>
					</view>
					<view>
						<button class="ss-reset-button tool-btn" @tap.stop="onDelete(order.id)" v-if="order.btns.includes('delete')">删除</button>
					</view>
				</view>
			</view>
		</view>
		<uni-load-more
			v-if="state.pagination.total > 0"
			:status="state.loadStatus"
			:content-text="{
				contentdown: '上拉加载更多',
			}"
			@tap="loadmore"
		/>
	</s-layout>
</template>

<script setup>
	import sheep from '@/sheep';
	import { onLoad, onReachBottom, onPullDownRefresh } from '@dcloudio/uni-app';
	import { computed, reactive } from 'vue';
	import _, { clone } from 'lodash';

	const pagination = {
		data: [],
		page: 1,
		total: 1,
		size: 6,
	};
	const state = reactive({
		currentTab: 0,
		showApply: false,
		pagination: {
			data: [],
			page: 1,
			total: 0,
			size: 6,
		},
		loadStatus: '',
	});
	const refundTypeList = [
		{
			text: '仅退款',
			value: 1,
		},
		{
			text: '退款/退货',
			value: 2,
		},
		// {
		// 	text: '换货',
		// 	value: 3,
		// },
	];
	const tabMaps = [
		{
			name: '全部',
			value: '',
		},
		{
			name: '申请中',
			value: '0',
		},
		{
			name: '退货中',
			value: '1',
		},
		{
			name: '已完成',
			value: '2',
		},
		{
			name: '已拒绝',
			value: '3',
		},
		{
			name: '已关闭',
			value: '4',
		},
	];

	/**
	 * 获取状态文本
	 * @param {number} status 状态
	 * @returns {string} 状态文本
	 */
	const getStatusText = (status) => {
		return tabMaps.find((item) => item.value == String(status))?.name || '';
	};

	/**
	 * 获取状态样式类
	 * @param {number} status 状态
	 * @returns {string} 状态样式类
	 */
	const getStatusClass = (status) => {
		const statusMap = {
			0: 'status-applying', // 申请中
			1: 'status-processing', // 处理中
			2: 'status-completed', // 已完成
			3: 'status-rejected', // 已拒绝
			4: 'status-closed', // 已关闭
		};
		return statusMap[status] || 'status-default';
	};

	/**
	 * 获取退款类型文本
	 * @param {number} type 退款类型
	 * @returns {string} 退款类型文本
	 */
	const getRefundTypeText = (type) => {
		return refundTypeList.find((item) => item.value == type)?.text || '';
	};

	/**
	 * 获取商品规格文本
	 * @param {string} spData 商品规格
	 * @returns {string} 商品规格文本
	 */
	const getSpDataValue = (spData) => {
		let str = '';
		const obj = JSON.parse(spData);
		Object.keys(obj).forEach((key) => {
			str += key + '：' + obj[key] + ' ';
		});
		return str;
	};

	// 切换选项卡
	function onTabsChange(e) {
		console.log('🚀 ~ onTabsChange ~ e:', e);
		if (state.currentTab === e.index) return;
		state.pagination = clone(pagination);
		state.currentTab = e.index;
		state.pagination.data = [];
		getOrderList();
	}

	// 获取售后列表
	async function getOrderList() {
		state.loadStatus = 'loading';
		let res = await sheep.$api.order.aftersale.list(
			{
				status: tabMaps[state.currentTab].value,
			},
			{ page: state.pagination.page - 1, size: state.pagination.size }
		);
		const { content, totalElements, totalPages } = res;
		state.pagination.data = _.concat(
			state.pagination.data,
			content.map((item) => {
				item.btns = [];

				switch (item.aftersaleStatus) {
					case 0:
						item.btns = ['cancel'];
						break;
					case 2:
					case 4:
						item.btns = ['delete'];
						break;
				}
				return item;
			})
		);
		state.pagination.total = totalElements;
		if (state.pagination.page < totalPages) {
			state.loadStatus = 'more';
		} else {
			state.loadStatus = 'noMore';
		}
	}

	function onApply(orderId) {
		uni.showModal({
			title: '提示',
			content: '确定要取消此申请吗？',
			success: async function (res) {
				if (res.confirm) {
					const res = await sheep.$api.order.aftersale.cancel(orderId);
					if (res) {
						sheep.$helper.toastSuccess('取消成功');
						state.pagination = clone(pagination);
						getOrderList();
					}
				}
			},
		});
	}

	function onDelete(orderId) {
		uni.showModal({
			title: '提示',
			content: '确定要删除吗？',
			success: async ({ confirm }) => {
				if (confirm) {
					const res = await sheep.$api.order.aftersale.delete(orderId);
					if (res == 1) {
						let index = state.pagination.data.findIndex((order) => order.id === orderId);
						state.pagination.data.splice(index, 1);
					}
				}
			},
		});
	}

	function handleDetail(order) {
		uni.$once('AFTERSALE_DETAIL', (e) => {
			if (e.action == 'delete') {
				let index = state.pagination.data.findIndex((order) => order.id === e.aftersaleId);
				state.pagination.data.splice(index, 1);
			} else if (e.action == 'cancel') {
				state.pagination = clone(pagination);
				getOrderList();
			}
		});
		sheep.$router.go('/pages/order/aftersale/detail', { id: order.orderId, aftersaleId: order.id });
	}

	onLoad(async (options) => {
		if (options.value) {
			state.currentTab = options.value;
		}
		getOrderList();
	});

	// 加载更多
	function loadmore() {
		if (state.loadStatus !== 'noMore') {
			state.pagination.page++;
			getOrderList();
		}
	}

	//下拉刷新
	onPullDownRefresh(() => {
		state.pagination = clone(pagination);
		getOrderList();
		setTimeout(function () {
			uni.stopPullDownRefresh();
		}, 800);
	});

	// 上拉加载更多
	onReachBottom(() => {
		loadmore();
	});
</script>

<style lang="scss" scoped>
	.aftersale-list-container {
		padding: 20rpx 20rpx;
		padding-bottom: 0;
	}

	.aftersale-card {
		background: #ffffff;
		border-radius: 16rpx;
		margin-bottom: 24rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
		transition: all 0.3s ease;
		position: relative;

		// 装饰性背景元素
		&::before {
			content: '';
			position: absolute;
			top: -20rpx;
			right: -40rpx;
			width: 120rpx;
			height: 120rpx;
			background: linear-gradient(135deg, $golden 0%, rgba($golden, 0.1) 100%);
			border-radius: 50%;
			opacity: 0.08;
			z-index: 1;
		}

		&:active {
			box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
		}

		.card-header {
			position: relative;
			padding: 30rpx;
			background: linear-gradient(135deg, #fff8f0 0%, #ffffff 100%);
			border-bottom: 1rpx solid #f8f9fa;
			display: flex;
			align-items: center;
			justify-content: space-between;
			z-index: 2;

			.order-info {
				flex: 1;

				.service-number {
					display: flex;
					align-items: center;

					.label {
						font-size: 28rpx;
						color: #666666;
						font-weight: 400;
					}

					.number {
						font-size: 28rpx;
						color: #333333;
						font-weight: 600;
						font-family: 'OPPOSANS', sans-serif;
					}
				}
			}

			.status-wrapper {
				.status-text {
					font-size: 26rpx;
					font-weight: 600;
				}
			}
		}

		.goods-section {
			position: relative;
			z-index: 2;

			.goods-item-wrapper {
				border-bottom: 1rpx solid #f8f9fa;

				&:last-child {
					border-bottom: none;
				}
			}
		}

		.apply-info-section {
			position: relative;
			padding: 24rpx 30rpx;
			background: linear-gradient(135deg, #fafafa 0%, #ffffff 100%);
			border-top: 1rpx solid #f8f9fa;
			display: flex;
			align-items: center;
			justify-content: space-between;
			z-index: 2;

			.apply-content {
				flex: 1;
				display: flex;
				align-items: center;
				gap: 20rpx;

				.apply-type {
					font-size: 26rpx;
					font-weight: 600;
					color: $golden;
					background: rgba($golden, 0.1);
					padding: 6rpx 12rpx;
					border-radius: 12rpx;
					border: 1rpx solid rgba($golden, 0.2);
				}

				.apply-reason {
					font-size: 24rpx;
					color: #666666;
					flex: 1;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
			}

			.arrow-icon {
				color: #cccccc;
				font-size: 24rpx;
				margin-left: 16rpx;
			}
		}
	}

	// 状态样式
	.status-applying {
		color: #1890ff !important;
	}

	.status-processing {
		color: $orange !important;
	}

	.status-completed {
		color: #52c41a !important;
	}

	.status-rejected {
		color: #ff4d4f !important;
	}

	.status-closed {
		color: #999999 !important;
	}

	.status-default {
		color: #666666 !important;
	}

	.tool-btn-box {
		padding: 20rpx;
		.tool-btn {
			width: 160rpx;
			height: 60rpx;
			color: #ff3000;
			background: #fee;
			border-radius: 30rpx;
			font-size: 26rpx;
			font-weight: 400;
			border: 1rpx solid #ff3000;
		}
		.apply-btn {
			width: 160rpx;
			height: 60rpx;
			color: #333;
			background: #fff;
			border-radius: 30rpx;
			font-size: 26rpx;
			font-weight: 400;
			border: 1rpx solid #999;
		}
	}
</style>
