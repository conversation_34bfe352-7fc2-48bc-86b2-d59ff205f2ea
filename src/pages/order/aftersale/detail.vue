<!-- 售后详情 -->
<template>
	<s-layout title="售后详情" :navbar="!isEmpty(state.info) && state.loading ? 'inner' : 'normal'">
		<view class="content_box" v-if="!isEmpty(state.info) && state.loading">
			<!-- 步骤条 -->
			<view
				class="steps-box ss-flex"
				:style="[
					{
						marginTop: '-' + Number(statusBarHeight + 88) + 'rpx',
						paddingTop: Number(statusBarHeight + 88) + 'rpx',
					},
				]"
			>
				<uni-steps style="width: 100%" :options="state.list" :active="state.active" active-color="#fff" />
				<!-- <view class="ss-flex">
					<view class="steps-item" v-for="(item, index) in state.list" :key="index">
						<view class="ss-flex">
							<text class="sicon-circlecheck" :class="state.active >= index ? 'activity-color' : 'info-color'"></text>

							<view v-if="state.list.length - 1 != index" class="line" :style="{width: (550/state.list.length) + 'rpx'}" :class="state.active >= index ? 'activity-bg' : 'info-bg'"></view>
						</view>
						<view class="steps-item-title" :class="state.active >= index ? 'activity-color' : 'info-color'">{{ item.title }}</view>
					</view>

				</view> -->
			</view>

			<!-- 服务状态 -->
			<view class="status-box ss-flex ss-col-center ss-row-between ss-m-x-20">
				<view class="">
					<view class="status-time">更新时间：{{ state.info.updateTime }}</view>
				</view>
				<!--        <view class="">-->
				<!--          <view class="status-text">{{ state.info.aftersale_status_desc }}</view>-->
				<!--          <view class="status-time">{{ state.info.update_time }}</view>-->
				<!--        </view>-->
				<!--        <text class="ss-iconfont _icon-forward" style="color: #666"></text>-->
			</view>

			<!-- 退款金额 -->
			<view class="aftersale-money ss-flex ss-col-center ss-row-between">
				<view class="aftersale-money--title">退款总额</view>
				<view class="aftersale-money--num">￥{{ state.info.returnAmount }}</view>
			</view>
			<!-- 服务商品 -->
			<view class="order-shop">
				<!--        <s-goods-item-->
				<!--          :title="state.info.goods_title"-->
				<!--          :price="state.info.goods_price"-->
				<!--          :img="state.info.goods_image"-->
				<!--          priceColor="#333333"-->
				<!--          :titleWidth="480"-->
				<!--          :skuText="state.info.goods_sku_text"-->
				<!--          :num="state.info.goods_num"-->
				<!--        ></s-goods-item>-->
				<view class="border-bottom" v-for="item in state.info.orderItemList" :key="item.id">
					<s-goods-item :img="item.pic" :title="item.productName" :skuText="item.spDataValue" :price="item.salePrice" :num="item.quantity" priceColor="#333333" :titleWidth="480"> </s-goods-item>
				</view>
			</view>

			<!-- 服务内容 -->
			<view class="aftersale-content">
				<view class="aftersale-item ss-flex ss-col-center" v-if="state.aftersaleId">
					<view class="item-title">售后单号：</view>
					<view class="item-content ss-m-r-16">{{ state.aftersaleId }}</view>
					<button class="ss-reset-button copy-btn" @tap="sheep.$helper.copyText(String(state.aftersaleId))">复制</button>
				</view>
				<view class="aftersale-item ss-flex ss-col-center">
					<view class="item-title">订单单号：</view>
					<view class="item-content ss-m-r-16">{{ state.info.orderId }}</view>
					<button class="ss-reset-button copy-btn" @tap="sheep.$helper.copyText(String(state.info.orderId))">复制</button>
				</view>
				<view class="aftersale-item ss-flex ss-col-center">
					<view class="item-title">申请时间：</view>
					<view class="item-content">{{ state.info.createTime }}</view>
				</view>
				<view class="aftersale-item ss-flex ss-col-center">
					<view class="item-title">售后类型：</view>
					<view class="item-content">{{ state.info.type === 1 ? '仅退款' : '退款退货' }}</view>
				</view>
				<view class="aftersale-item ss-flex ss-col-center">
					<view class="item-title">申请原因：</view>
					<view class="item-content">{{ state.info.reason || '--' }}</view>
				</view>
				<view class="aftersale-item ss-flex ss-col-center">
					<view class="item-title">相关描述：</view>
					<view class="item-content">{{ state.info.description || '--' }}</view>
				</view>
				<view class="aftersale-proof-item ss-flex ss-col-center" v-if="state.info.proofPics">
					<view class="item-title">相关凭证：</view>
					<view class="item-content">
						<view class="proof-item" v-for="item in state.info.proofPics.split(',')" :key="item">
							<image :src="item" />
						</view>
					</view>
				</view>
			</view>
		</view>
		<s-empty v-if="isEmpty(state.info) && state.loading" icon="/static/order-empty.png" text="暂无该订单售后详情" />
		<su-fixed bottom placeholder bg="bg-white" v-if="!isEmpty(state.info)">
			<view class="foot_box ss-flex-gap-20">
				<button class="ss-reset-button contcat-btn" v-if="state.info.status == 0" @tap="onApply(state.info.orderId)">取消申请</button>
				<button class="ss-reset-button btn" v-if="[2, 4].includes(state.info.status) && state.aftersaleId" @tap="onDelete(state.info.id)">删除</button>
				<button class="ss-reset-button contcat-btn" open-type="contact">联系客服</button>
			</view>
		</su-fixed>
	</s-layout>
</template>

<script setup>
	import sheep from '@/sheep';
	import { onLoad } from '@dcloudio/uni-app';
	import { reactive } from 'vue';
	import { isEmpty } from 'lodash';

	// 获取状态栏高度
	const statusBarHeight = sheep.$platform.device.statusBarHeight * 2;

	// 获取头部背景图
	const headerBg = sheep.$url.css('/2025/06/048112904105d346239b6b0e1bf244cb8dorder_bg.png');

	// 状态
	const state = reactive({
		active: 0,
		orderId: null,
		aftersaleId: 0,
		info: {},
		list: [
			{
				title: '提交申请',
			},
			{
				title: '处理中',
			},
		],
		loading: false,
	});

	/**
	 * 取消售后
	 * @param {string} orderId 订单号
	 */
	function onApply(orderId) {
		uni.showModal({
			title: '提示',
			content: '确定要取消此申请吗？',
			success: async function (res) {
				if (res.confirm) {
					await sheep.$api.order.aftersale.cancel(orderId);
					sheep.$helper.toastSuccess('取消成功');
					uni.$emit('AFTERSALE_DETAIL', { action: 'cancel', orderId: state.orderId });
					setTimeout(() => {
						sheep.$router.back();
					}, 1000);
				}
			},
		});
	}

	/**
	 * 删除售后
	 * @param {string} orderId 订单号
	 */
	function onDelete() {
		uni.showModal({
			title: '提示',
			content: '确定要删除吗？',
			success: async function ({ confirm }) {
				if (confirm) {
					const res = await sheep.$api.order.aftersale.delete(state.aftersaleId);
					if (res == 1) {
						sheep.$helper.toastSuccess('删除成功');
						uni.$emit('AFTERSALE_DETAIL', { action: 'delete', aftersaleId: state.aftersaleId });
						setTimeout(() => {
							sheep.$router.back();
						}, 1000);
					}
				}
			},
		});
	}

	/**
	 * 获取售后详情
	 * @param {string} id 售后单号
	 */
	async function getDetail(id) {
		const data = await sheep.$api.order.aftersale.detail(id);
		state.loading = true;
		if (data) {
			data.orderItemList.forEach((item) => {
				let str = '';
				const obj = JSON.parse(item.spData);
				Object.keys(obj).forEach((key) => {
					str += key + '：' + obj[key] + ' ';
				});
				item.spDataValue = str;
			});
			state.info = data;
			if (state.info.type === 1) {
				//仅退款
				state.list.push({ title: state.info.status == 2 ? '已完成' : '已关闭' });
				if (state.info.status === 4 || state.info.status === 2) {
					state.active = 2;
				} else {
					state.active = 1;
				}
			} else if (state.info.type === 2) {
				// 退货退款
				state.list.push({ title: '退货中' });
				if (state.info.status === 2) {
					state.active = 3;
					state.list.push({ title: '已完成' });
				} else {
					state.active = 1;
				}
			}
		} else {
			state.info = null;
		}
	}

	/**
	 * 生命周期
	 */
	onLoad((options) => {
		state.orderId = options.id;
		state.aftersaleId = options.aftersaleId;
		getDetail(options.id);
	});
</script>

<style lang="scss" scoped>
	// 步骤条
	.steps-box {
		width: 100%;
		height: 190rpx;
		background: v-bind(headerBg) no-repeat, linear-gradient(90deg, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
		background-size: 750rpx 100%;
		// padding-left: 72rpx;

		.steps-item {
			.sicon-circleclose {
				font-size: 24rpx;
				color: #fff;
			}
			.sicon-circlecheck {
				font-size: 24rpx;
			}
			.steps-item-title {
				font-size: 24rpx;
				font-weight: 400;
				margin-top: 16rpx;
				margin-left: -36rpx;
				width: 100rpx;
				text-align: center;
			}
		}
	}
	.activity-color {
		color: #fff;
	}
	.info-color {
		color: rgba(#fff, 0.4);
	}
	.activity-bg {
		background: #fff;
	}
	.info-bg {
		background: rgba(#fff, 0.4);
	}

	.line {
		width: 180rpx;
		height: 4rpx;
	}

	// 服务状态
	.status-box {
		position: relative;
		z-index: 3;
		background-color: #fff;
		border-radius: 20rpx 20rpx 0px 0px;
		padding: 20rpx;
		margin-top: -20rpx;

		.status-text {
			font-size: 28rpx;

			font-weight: 500;
			color: rgba(51, 51, 51, 1);
			margin-bottom: 20rpx;
		}

		.status-time {
			font-size: 24rpx;

			font-weight: 400;
			color: rgba(153, 153, 153, 1);
		}
	}

	// 退款金额
	.aftersale-money {
		background-color: #fff;
		height: 98rpx;
		padding: 0 20rpx;
		margin: 20rpx;

		.aftersale-money--title {
			font-size: 28rpx;

			font-weight: 500;
			color: rgba(51, 51, 51, 1);
		}

		.aftersale-money--num {
			font-size: 28rpx;
			font-family: OPPOSANS;
			font-weight: 500;
			color: #ff3000;
		}
	}

	// order-shop
	.order-shop {
		padding: 20rpx;
		background-color: #fff;
		margin: 0 20rpx 20rpx 20rpx;
	}

	// 服务内容
	.aftersale-content {
		background-color: #fff;
		padding: 20rpx;
		margin: 0 20rpx;

		.aftersale-item {
			height: 60rpx;

			.copy-btn {
				background: #eeeeee;
				color: #333;
				border-radius: 20rpx;
				width: 75rpx;
				height: 40rpx;
				font-size: 22rpx;
			}

			.item-title {
				color: #999;
				font-size: 28rpx;
			}

			.item-content {
				color: #333;
				font-size: 28rpx;
			}
		}

		.aftersale-proof-item {
			.item-title {
				color: #999;
				font-size: 28rpx;
			}

			.item-content {
				display: flex;
				flex-wrap: wrap;
				gap: 10rpx;

				.proof-item {
					width: 168rpx;
					height: 168rpx;
					border-radius: 12rpx;
					image {
						width: 100%;
						height: 100%;
					}
				}
			}
		}
	}

	// 底部功能
	.foot_box {
		height: 100rpx;
		background-color: #fff;
		display: flex;
		align-items: center;
		justify-content: flex-end;
		padding: 0 20px;

		.btn {
			width: 160rpx;
			line-height: 60rpx;
			background: #fee;
			border: 2rpx #ff3000 solid;
			color: #ff3000;
			border-radius: 30rpx;
			padding: 0;
			font-size: 26rpx;
			font-weight: 400;
		}

		.contcat-btn {
			width: 160rpx;
			line-height: 60rpx;
			background: #fff;
			border: 2rpx #999 solid;
			color: #333;
			border-radius: 30rpx;
			padding: 0;
			font-size: 26rpx;
			font-weight: 400;
		}
	}
</style>
