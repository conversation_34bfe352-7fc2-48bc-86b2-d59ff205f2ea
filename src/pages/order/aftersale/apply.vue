<!-- 申请售后 -->
<template>
	<s-layout title="申请售后" bgColor="#f8f9fa">
		<!-- 商品信息卡片 -->
		<view class="goods-card ss-m-20 ss-r-16">
			<view class="card-header">
				<view class="header-icon">
					<text class="cicon-goods"></text>
				</view>
				<text class="header-title">售后商品</text>
			</view>
			<view class="goods-list">
				<view class="goods-item" v-for="item in state.goodsItem" :key="item.id">
					<s-goods-item :img="item.pic" :title="item.productName" :skuText="item.spDataValue" :price="item.salePrice" :num="item.quantity"></s-goods-item>
				</view>
			</view>
		</view>

		<!-- 售后信息表单 -->
		<view class="form-card ss-m-20 ss-r-16">
			<uni-forms ref="form" v-model="formData" :rules="rules" label-position="top">
				<!-- 售后类型 -->
				<view class="form-section">
					<view class="section-header">
						<view class="section-icon">
							<text class="cicon-settings"></text>
						</view>
						<text class="section-title">售后类型</text>
					</view>
					<view class="radio-group-wrapper">
						<radio-group @change="onRefundChange">
							<label class="radio-item" v-for="(item, index) in state.refundTypeList" :key="index">
								<radio :checked="formData.applyRefundType === item.value" color="var(--ui-BG-Main)" :value="item.value" :disabled="state.disabled" />
								<view class="radio-text">{{ item.text }}</view>
							</label>
						</radio-group>
					</view>
				</view>

				<!-- 申请原因 -->
				<view class="form-section">
					<view class="reason-selector" @tap="state.showModal = true">
						<view class="selector-left">
							<view class="section-icon">
								<text class="cicon-comment"></text>
							</view>
							<text class="section-title">申请原因</text>
						</view>
						<view class="selector-right">
							<text class="reason-text" v-if="formData.reason">{{ formData.reason }}</text>
							<text class="reason-placeholder" v-else>请选择申请原因</text>
							<text class="cicon-forward"></text>
						</view>
					</view>
				</view>
				<!-- 联系方式 -->
				<!-- <view class="form-section">
					<view class="section-header">
						<view class="section-icon">
							<text class="cicon-phone"></text>
						</view>
						<text class="section-title">联系方式</text>
					</view>
					<view class="contact-input-wrapper">
						<uni-easyinput :inputBorder="false" type="number" v-model="formData.mobile" placeholder="请输入您的联系电话" class="contact-input" />
					</view>
				</view> -->
				<!-- 留言 -->
				<view class="form-section" v-if="state.currentStatus == 3">
					<view class="section-header">
						<view class="section-icon">
							<text class="cicon-info"></text>
						</view>
						<text class="section-title">相关描述</text>
					</view>
					<view class="description-wrapper">
						<view class="textarea-wrapper">
							<uni-easyinput :inputBorder="false" type="textarea" maxlength="120" autoHeight v-model="formData.description" placeholder="请详细描述您遇到的问题，以便我们更好地为您处理" class="description-textarea" />
						</view>
						<view class="upload-section">
							<view class="upload-header">
								<text class="upload-title">上传图片</text>
								<text class="upload-tip">最多可上传9张图片</text>
							</view>
							<view class="upload-area">
								<s-uploader v-model:url="formData.images" fileMediatype="image" limit="9" mode="grid" :imageStyles="{ width: '168rpx', height: '168rpx', borderRadius: '12rpx' }" />
							</view>
						</view>
					</view>
				</view>
			</uni-forms>
		</view>

		<!-- 底部操作区 -->
		<su-fixed bottom placeholder>
			<view class="bottom-actions">
				<view class="action-buttons">
					<button open-type="contact" class="contact-btn">
						<text class="cicon-customer-service"></text>
						<text>联系客服</text>
					</button>
					<button class="submit-btn ui-BG-Main-Gradient" @tap="submit">
						<text class="cicon-check-round"></text>
						<text>提交申请</text>
					</button>
				</view>
			</view>
		</su-fixed>
		<!-- 申请原因选择弹窗 -->
		<su-popup :show="state.showModal" round="20" :showClose="true" @close="state.showModal = false">
			<view class="reason-modal">
				<view class="modal-header">
					<view class="header-icon">
						<text class="cicon-comment"></text>
					</view>
					<text class="header-title">选择申请原因</text>
				</view>
				<view class="modal-content">
					<radio-group @change="onChange">
						<label class="reason-option" v-for="item in state.refundReasonList" :key="item.value">
							<view class="option-content">
								<text class="option-text">{{ item.title }}</text>
								<radio :value="item.value" color="var(--ui-BG-Main)" :checked="item.value === state.currentValue" />
							</view>
						</label>
					</radio-group>
				</view>
				<view class="modal-footer">
					<button class="confirm-btn ui-BG-Main-Gradient" @tap="onReason">
						<text class="cicon-check-round"></text>
						<text>确定</text>
					</button>
				</view>
			</view>
		</su-popup>
	</s-layout>
</template>

<script setup>
	import sheep from '@/sheep';
	import { onLoad } from '@dcloudio/uni-app';
	import { reactive, ref, unref, computed } from 'vue';
	// 表单
	const form = ref(null);

	// 状态
	const state = reactive({
		disabled: false,
		showModal: false,
		currentValue: 0,
		currentStatus: null,
		goodsItem: [],
		// showSuccess: false,
		reasonText: '',
		//售后类型
		refundTypeList: [
			{
				text: '仅退款',
				value: 1,
			},
			{
				text: '退款/退货',
				value: 2,
			},
			// {
			// 	text: '换货',
			// 	value: 3,
			// },
		],
		refundReasonList: [
			{
				value: '1',
				title: '卖家发错货了',
			},
			{
				value: '2',
				title: '退运费',
			},
			{
				value: '3',
				title: '大小/重量与商品描述不符',
			},
			{
				value: '4',
				title: '生产日期/保质期与商品描述不符',
			},
			{
				value: '5',
				title: '质量问题',
			},
			{
				value: '6',
				title: '我不想要了',
			},
		],
	});

	const formData = reactive({
		orderId: '',
		applyRefundType: '',
		reason: '',
		description: '',
		images: [],
		proofPics: [],
		mobile: '',
		quantity: null,
	});

	const rules = reactive({});

	// 用户信息
	const userInfo = computed(() => sheep.$store('user').userInfo);

	// 提交表单
	async function submit() {
		// 用户绑定了接单员要传接单员会员id
		if (userInfo.value.spreadUid && userInfo.value.isBindSpread == 1) {
			formData.orderTakerId = userInfo.value.spreadUid;
		}

		if (formData.images.length > 0) {
			formData.proofPics = formData.images.join(',');
		}

		let data = { ...formData };

		if (!data.applyRefundType) {
			sheep.$helper.toast('请选择售后类型');
			return;
		}
		if (!data.reason) {
			sheep.$helper.toast('请选择申请原因');
			return;
		}

		uni.showLoading({
			title: '提交中...',
		});
		try {
			await sheep.$api.order.applyRefund(data);

			sheep.$helper.toastSuccess('申请成功');

			setTimeout(() => {
				sheep.$router.go('/pages/order/list', { type: 4 });
			}, 1000);
		} catch (error) {
			console.log(error);
		} finally {
			uni.hideLoading();
		}
	}

	//选择售后类型
	function onRefundChange(e) {
		formData.applyRefundType = e.detail.value;
	}

	//选择申请原因
	function onChange(e) {
		state.currentValue = e.detail.value;
		state.refundReasonList.forEach((item) => {
			if (item.value === e.detail.value) {
				state.reasonText = item.title;
			}
		});
	}
	//确定
	function onReason() {
		formData.reason = state.reasonText;
		state.showModal = false;
	}

	function onTitle(e, title) {
		state.currentValue = e;
		state.reasonText = title;
	}

	/**
	 * @param {*} options
	 */
	onLoad((options) => {
		// 获取订单详情
		const order = JSON.parse(options.item);
		// 获取订单商品
		state.goodsItem = order.orderItemList;
		formData.orderId = order.orderId;
		// 当前订单状态
		state.currentStatus = order.status;
		// 计算订单商品数量
		let sum = 0;
		order.orderItemList.forEach((it) => {
			sum += it.quantity;
		});
		formData.quantity = sum;
		// 目前只能退款，先写死
		// formData.applyRefundType = 1
		// state.disabled = true
		if (order.status == 1) {
			formData.applyRefundType = 1;
			state.disabled = true;
		} else if (order.status == 3) {
			formData.applyRefundType = 2;
		} else {
			state.disabled = false;
		}
	});
</script>

<style lang="scss" scoped>
	// 商品信息卡片
	.goods-card {
		background: #ffffff;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
		overflow: hidden;

		.card-header {
			display: flex;
			align-items: center;
			padding: 30rpx;
			background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
			border-bottom: 1rpx solid #f0f0f0;

			.header-icon {
				width: 40rpx;
				height: 40rpx;
				border-radius: 50%;
				background: var(--ui-BG-Main);
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 16rpx;

				.cicon-goods {
					font-size: 20rpx;
					color: #ffffff;
				}
			}

			.header-title {
				font-size: 32rpx;
				font-weight: 600;
				color: #333333;
			}
		}

		.goods-list {
			.goods-item {
				padding: 0 30rpx;
				border-bottom: 1rpx solid #f8f9fa;

				&:last-child {
					border-bottom: none;
				}
			}
		}
	}

	// 表单卡片
	.form-card {
		background: #ffffff;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
		overflow: hidden;

		.form-section {
			padding: 30rpx;
			border-bottom: 1rpx solid #f8f9fa;

			&:last-child {
				border-bottom: none;
			}

			.section-header {
				display: flex;
				align-items: center;
				margin-bottom: 24rpx;

				.section-icon {
					width: 36rpx;
					height: 36rpx;
					border-radius: 50%;
					background: linear-gradient(135deg, var(--ui-BG-Main) 0%, var(--ui-BG-Main-gradient) 100%);
					display: flex;
					align-items: center;
					justify-content: center;
					margin-right: 16rpx;

					.cicon-settings,
					.cicon-comment,
					.cicon-goods,
					.cicon-phone,
					.cicon-info {
						font-size: 16rpx;
						color: #ffffff;
					}
				}

				.section-title {
					font-size: 32rpx;
					font-weight: 600;
					color: #333333;
				}
			}
		}
	}

	// 单选框组样式
	.radio-group-wrapper {
		.radio-item {
			display: flex;
			align-items: center;
			padding: 20rpx 0;
			border-bottom: 1rpx solid #f8f9fa;
			transition: all 0.3s ease;

			&:last-child {
				border-bottom: none;
			}

			&:active {
				background: #fafafa;
			}

			radio {
				margin-right: 20rpx;
				transform: scale(0.9);
			}

			.radio-text {
				font-size: 30rpx;
				color: #333333;
				font-weight: 500;
			}
		}
	}

	// 原因选择器
	.reason-selector {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 24rpx 0;
		cursor: pointer;
		transition: all 0.3s ease;

		&:active {
			opacity: 0.8;
		}

		.selector-left {
			display: flex;
			align-items: center;

			.section-icon {
				width: 36rpx;
				height: 36rpx;
				border-radius: 50%;
				background: linear-gradient(135deg, var(--ui-BG-Main) 0%, var(--ui-BG-Main-gradient) 100%);
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 16rpx;

				.cicon-comment {
					font-size: 18rpx;
					color: #ffffff;
				}
			}

			.section-title {
				font-size: 32rpx;
				font-weight: 600;
				color: #333333;
			}
		}

		.selector-right {
			display: flex;
			align-items: center;

			.reason-text {
				font-size: 28rpx;
				color: var(--ui-BG-Main);
				font-weight: 500;
				margin-right: 12rpx;
			}

			.reason-placeholder {
				font-size: 28rpx;
				color: #999999;
				margin-right: 12rpx;
			}

			.cicon-forward {
				font-size: 24rpx;
				color: #cccccc;
			}
		}
	}

	// 联系方式输入框
	.contact-input-wrapper {
		.contact-input {
			background: #f8f9fa;
			border-radius: 16rpx;
		}
	}

	// 描述和上传区域
	.description-wrapper {
		.textarea-wrapper {
			margin-bottom: 32rpx;

			.description-textarea {
				background: #f8f9fa;
				border-radius: 16rpx;
			}
		}

		.upload-section {
			.upload-header {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 20rpx;

				.upload-title {
					font-size: 28rpx;
					font-weight: 600;
					color: #333333;
				}

				.upload-tip {
					font-size: 24rpx;
					color: #999999;
				}
			}

			.upload-area {
				background: #f8f9fa;
				border-radius: 16rpx;
				padding: 20rpx;
				border: 2rpx dashed #e0e0e0;
				transition: all 0.3s ease;

				&:hover {
					border-color: var(--ui-BG-Main);
					background: rgba(233, 180, 97, 0.05);
				}
			}
		}
	}

	// 底部操作区
	.bottom-actions {
		background: #ffffff;
		padding: 20rpx 30rpx;
		box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.06);

		.action-buttons {
			display: flex;
			gap: 20rpx;

			.contact-btn {
				flex: 1;
				height: 88rpx;
				background: #f8f9fa;
				border: 2rpx solid #e9ecef;
				border-radius: 44rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 28rpx;
				color: #666666;
				font-weight: 500;
				transition: all 0.3s ease;

				&:active {
					background: #e9ecef;
					transform: scale(0.98);
				}

				.cicon-customer-service {
					font-size: 32rpx;
					margin-right: 8rpx;
				}
			}

			.submit-btn {
				flex: 2;
				height: 88rpx;
				border-radius: 44rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 30rpx;
				color: #ffffff;
				font-weight: 600;
				box-shadow: 0 6rpx 20rpx rgba(233, 180, 97, 0.3);
				transition: all 0.3s ease;

				&:active {
					transform: scale(0.98);
					box-shadow: 0 4rpx 16rpx rgba(233, 180, 97, 0.4);
				}

				.cicon-check-round {
					font-size: 32rpx;
					margin-right: 8rpx;
				}
			}
		}
	}

	// 弹窗样式
	.reason-modal {
		width: 750rpx;
		background: #ffffff;
		border-radius: 32rpx 32rpx 0 0;
		overflow: hidden;

		.modal-header {
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 40rpx 30rpx 30rpx;
			background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
			border-bottom: 1rpx solid #f0f0f0;

			.header-icon {
				width: 48rpx;
				height: 48rpx;
				border-radius: 50%;
				background: var(--ui-BG-Main);
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 16rpx;

				.cicon-comment {
					font-size: 24rpx;
					color: #ffffff;
				}
			}

			.header-title {
				font-size: 34rpx;
				font-weight: 600;
				color: #333333;
			}
		}

		.modal-content {
			overflow-y: auto;

			.reason-option {
				display: block;
				border-bottom: 1rpx solid #f8f9fa;
				transition: all 0.3s ease;

				&:last-child {
					border-bottom: none;
				}

				&:active {
					background: #fafafa;
				}

				.option-content {
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: 32rpx 30rpx;

					.option-text {
						font-size: 30rpx;
						color: #333333;
						font-weight: 500;
						flex: 1;
					}

					radio {
						transform: scale(0.9);
					}
				}
			}
		}

		.modal-footer {
			padding: 20rpx 30rpx;
			background: #fafafa;

			.confirm-btn {
				width: 100%;
				height: 88rpx;
				border-radius: 44rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 30rpx;
				color: #ffffff;
				font-weight: 600;
				box-shadow: 0 6rpx 20rpx rgba(233, 180, 97, 0.3);
				transition: all 0.3s ease;

				&:active {
					transform: scale(0.98);
					box-shadow: 0 4rpx 16rpx rgba(233, 180, 97, 0.4);
				}

				.cicon-check-round {
					font-size: 32rpx;
					margin-right: 8rpx;
				}
			}
		}
	}
</style>
