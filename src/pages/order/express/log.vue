<!-- 物流追踪 -->
<template>
	<s-layout title="物流追踪">
		<view class="log-wrap">
			<!-- 物流状态与收货地址合并卡片 -->
			<view class="express-address-card" v-if="state.info.expressNo || state.info.addressInfo">
				<view class="status-background">
					<view class="status-content">
						<!-- 物流状态区域 -->
						<view class="status-header" v-if="state.info.expressNo">
							<view class="status-icon">
								<!-- <uni-icons type="location-filled" size="20" color="#ffffff"></uni-icons> -->
								<text class="cicon-time"></text>
							</view>
							<view class="status-info">
								<text class="status-title">{{ getExpressStatus() }}</text>
								<view class="status-subtitle-container">
									<text class="status-subtitle">{{ logisticsMap[state.info.expressName] }} {{ state.info.expressNo }}</text>
									<view class="copy-btn" @click.stop="copyExpressNo" v-if="state.info.expressNo">
										<uni-icons type="copy" size="16" color="rgba(255, 255, 255, 0.9)"></uni-icons>
									</view>
								</view>
							</view>
						</view>

						<!-- 最新物流动态 -->
						<!-- <view class="latest-update" v-if="state.info.expressDetail && state.info.expressDetail.length > 0">
						<text class="update-text">{{ state.info.expressDetail[0].title }}</text>
						<text class="update-time">{{ state.info.expressDetail[0].desc }}</text>
						</view> -->

						<!-- 收货地址区域 -->
						<view class="address-section" v-if="state.info.addressInfo">
							<view class="address-divider"></view>
							<view class="address-header">
								<uni-icons type="location" size="16" color="#ffffff"></uni-icons>
								<text class="address-title">送达地址</text>
							</view>
							<view class="address-content">
								<view class="address-user">
									<text class="user-name">{{ state.info.addressInfo.name }}</text>
									<text class="user-phone">{{ state.info.addressInfo.userPhone }}</text>
								</view>
								<text class="address-detail">{{ state.info.addressInfo.area }} {{ state.info.addressInfo.address }}</text>
							</view>
						</view>
					</view>

					<!-- 装饰性元素 -->
					<view class="decoration-circle decoration-1"></view>
					<view class="decoration-circle decoration-2"></view>
					<view class="decoration-line"></view>
				</view>
			</view>

			<!-- 物流轨迹 -->
			<view class="track-card">
				<view class="track-header">
					<view class="track-icon">
						<text class="cicon-event-list"></text>
					</view>
					<text class="track-title">物流轨迹</text>
				</view>
				<view class="track-content" v-if="displayedTrackList.length > 0">
					<uni-steps :options="displayedTrackList" active-color="#f3a73f" :active="0" direction="column" />
					<view class="track-toggle" v-if="hasMoreTracks" @click="toggleTrackExpanded">
						<text class="track-toggle-text">{{ state.isTrackExpanded ? '收起' : `查看全部${state.info.expressDetail.length}条` }}</text>
						<uni-icons :type="state.isTrackExpanded ? 'up' : 'down'" size="16" color="#999999" :class="['track-arrow', { 'track-arrow-expanded': state.isTrackExpanded }]"> </uni-icons>
					</view>
				</view>
				<view class="track-empty" v-else>
					<s-empty text="暂无物流记录" :icon="sheep.$url.static('/2023/09/148af502fc240448108dae21bc5c096efemessage.png')"></s-empty>
				</view>
			</view>

			<!-- 商品信息卡片 -->
			<view class="goods-card" v-if="state.info.productInfo && state.info.productInfo.length > 0">
				<view class="goods-header">
					<view class="goods-icon">
						<text class="cicon-goods"></text>
					</view>
					<text class="goods-title">商品信息</text>
					<text class="goods-count">共{{ state.info.productInfo.length }}件商品</text>
				</view>
				<view class="goods-list">
					<view class="goods-item" v-for="(item, index) in state.info.productInfo" :key="index">
						<s-goods-item :img="item.pic" :title="item.productName" :skuText="item.spDataValue" :price="item.salePrice" :num="item.quantity"></s-goods-item>
					</view>
				</view>
			</view>
		</view>
	</s-layout>
</template>

<script setup>
	import sheep from '@/sheep';
	import { onLoad } from '@dcloudio/uni-app';
	import { reactive, computed } from 'vue';

	const state = reactive({
		info: {},
		isTrackExpanded: false, // 物流轨迹展开状态
		previewCount: 3, // 默认显示的物流记录条数
	});

	// 物流公司
	const logisticsMap = {
		shunfenglengyun: '顺丰快递',
		jtexpress: '极兔快递',
		yunda: '韵达快递',
		shentong: '申通快递',
		zhongtong: '中通快递',
		jd: '京东快递',
		ems: 'EMS快递',
		yuantong: '圆通快递',
	};

	// 计算要显示的物流记录
	const displayedTrackList = computed(() => {
		if (!state.info.expressDetail || state.info.expressDetail.length === 0) {
			return [];
		}

		if (state.isTrackExpanded) {
			return state.info.expressDetail; // 展开时显示全部
		} else {
			return state.info.expressDetail.slice(0, state.previewCount); // 收缩时显示前几条
		}
	});

	// 是否有更多物流记录
	const hasMoreTracks = computed(() => {
		return state.info.expressDetail && state.info.expressDetail.length > state.previewCount;
	});

	// 获取物流状态文本
	function getExpressStatus() {
		if (!state.info.expressDetail || state.info.expressDetail.length === 0) {
			return '暂无物流信息';
		}

		const latestStatus = state.info.expressDetail[0].title;
		if (latestStatus.includes('已签收') || latestStatus.includes('已送达')) {
			return '已送达';
		} else if (latestStatus.includes('派送中') || latestStatus.includes('正在派送')) {
			return '派送中';
		} else if (latestStatus.includes('运输中') || latestStatus.includes('在途')) {
			return '运输中';
		} else if (latestStatus.includes('已发货') || latestStatus.includes('已揽收')) {
			return '已发货';
		} else {
			return '处理中';
		}
	}

	// 切换物流轨迹展开状态
	function toggleTrackExpanded() {
		state.isTrackExpanded = !state.isTrackExpanded;
	}

	// 复制快递单号
	function copyExpressNo() {
		if (!state.info.expressNo) {
			uni.showToast({
				title: '暂无快递单号',
				icon: 'none',
			});
			return;
		}

		uni.setClipboardData({
			data: state.info.expressNo,
			success: () => {
				uni.showToast({
					title: '复制成功',
					icon: 'success',
				});
			},
			fail: () => {
				uni.showToast({
					title: '复制失败',
					icon: 'none',
				});
			},
		});
	}

	// 获取物流详情
	async function getExpressDetail(id, orderId) {
		const res = await sheep.$api.order.expressOther(orderId);

		// 处理物流轨迹数据
		if (res.expressDetail && res.expressDetail.length) {
			res.expressDetail.map((item) => {
				item.title = item.context;
				item.desc = item.fttime;
			});
		}

		// 处理商品规格数据
		if (res.productInfo && res.productInfo.length) {
			res.productInfo.forEach((item) => {
				if (item.spData) {
					try {
						const obj = JSON.parse(item.spData);
						let str = '';
						Object.keys(obj).forEach((key) => {
							str += key + '：' + obj[key] + ' ';
						});
						item.spDataValue = str.trim();
					} catch (e) {
						item.spDataValue = '';
					}
				}
			});
		}

		state.info = res;
	}

	onLoad((options) => {
		getExpressDetail(options.id, options.orderId);
	});
</script>

<style lang="scss" scoped>
	.log-wrap {
		padding: 20rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
	}

	// 物流状态与收货地址合并卡片
	.express-address-card {
		margin-bottom: 24rpx;
		border-radius: 20rpx;
		overflow: hidden;

		.status-background {
			position: relative;
			background: linear-gradient(135deg, #f3a73f 0%, #ff8533 100%);
			padding: 32rpx 24rpx;
			overflow: hidden;

			.status-content {
				position: relative;
				z-index: 2;

				.status-header {
					display: flex;
					align-items: center;
					margin-bottom: 20rpx;

					.status-icon {
						width: 60rpx;
						height: 60rpx;
						background: rgba(255, 255, 255, 0.2);
						border-radius: 50%;
						display: flex;
						align-items: center;
						justify-content: center;
						margin-right: 20rpx;

						.cicon-time {
							font-size: 30rpx;
							color: #ffffff;
						}
					}

					.status-info {
						flex: 1;

						.status-title {
							display: block;
							font-size: 36rpx;
							font-weight: 700;
							color: #ffffff;
							margin-bottom: 8rpx;
						}

						.status-subtitle-container {
							display: flex;
							align-items: center;
							justify-content: space-between;

							.status-subtitle {
								display: block;
								font-size: 26rpx;
								color: rgba(255, 255, 255, 0.9);
								flex: 1;
							}

							.copy-btn {
								margin-left: 12rpx;
								padding: 8rpx;
								border-radius: 6rpx;
								background: rgba(255, 255, 255, 0.15);
								display: flex;
								align-items: center;
								justify-content: center;
								backdrop-filter: blur(10rpx);
								transition: all 0.3s ease;

								&:active {
									background: rgba(255, 255, 255, 0.25);
									transform: scale(0.95);
								}
							}
						}
					}
				}

				.latest-update {
					background: rgba(255, 255, 255, 0.15);
					border-radius: 12rpx;
					padding: 20rpx;
					backdrop-filter: blur(10rpx);
					margin-bottom: 20rpx;

					.update-text {
						display: block;
						font-size: 28rpx;
						color: #ffffff;
						font-weight: 500;
						margin-bottom: 8rpx;
						line-height: 1.4;
					}

					.update-time {
						display: block;
						font-size: 24rpx;
						color: rgba(255, 255, 255, 0.8);
					}
				}

				// 收货地址区域
				.address-section {
					.address-divider {
						height: 1rpx;
						background: rgba(255, 255, 255, 0.2);
						margin: 20rpx 0;
					}

					.address-header {
						display: flex;
						align-items: center;
						margin-bottom: 16rpx;

						.address-title {
							font-size: 28rpx;
							font-weight: 600;
							color: #ffffff;
							margin-left: 8rpx;
						}
					}

					.address-content {
						.address-user {
							display: flex;
							align-items: center;
							margin-bottom: 12rpx;

							.user-name {
								font-size: 28rpx;
								font-weight: 600;
								color: #ffffff;
								margin-right: 20rpx;
							}

							.user-phone {
								font-size: 26rpx;
								color: rgba(255, 255, 255, 0.9);
							}
						}

						.address-detail {
							font-size: 26rpx;
							color: rgba(255, 255, 255, 0.9);
							line-height: 1.5;
						}
					}
				}
			}

			// 装饰性元素
			.decoration-circle {
				position: absolute;
				border-radius: 50%;
				background: rgba(255, 255, 255, 0.1);

				&.decoration-1 {
					width: 120rpx;
					height: 120rpx;
					top: -40rpx;
					right: -20rpx;
				}

				&.decoration-2 {
					width: 80rpx;
					height: 80rpx;
					bottom: -20rpx;
					left: -10rpx;
				}
			}

			.decoration-line {
				position: absolute;
				top: 50%;
				right: -50rpx;
				width: 100rpx;
				height: 2rpx;
				background: rgba(255, 255, 255, 0.2);
				transform: translateY(-50%) rotate(45deg);
			}
		}
	}

	// 通用卡片样式
	.card-base {
		background: #ffffff;
		border-radius: 16rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
		overflow: hidden;
	}

	.header-base {
		display: flex;
		align-items: center;
		padding: 24rpx 24rpx 16rpx 24rpx;
		border-bottom: 1rpx solid #f0f0f0;

		.icon {
			margin-right: 12rpx;
		}

		.title {
			font-size: 28rpx;
			font-weight: 600;
			color: #333333;
			flex: 1;
		}
	}

	// 商品信息卡片
	.goods-card {
		@extend .card-base;

		.goods-header {
			@extend .header-base;

			.goods-icon {
				@extend .icon;

				.cicon-goods {
					font-size: 30rpx;
					color: #f3a73f;
				}
			}

			.goods-title {
				@extend .title;
			}

			.goods-count {
				font-size: 26rpx;
				color: #999999;
			}
		}

		.goods-list {
			padding: 0 24rpx 24rpx 24rpx;
		}

		.goods-item {
			display: flex;
			align-items: flex-start;
			padding: 20rpx 0;
			border-bottom: 1rpx solid #f0f0f0;

			&:last-child {
				border-bottom: none;
			}

			.goods-image {
				width: 120rpx;
				height: 120rpx;
				border-radius: 12rpx;
				margin-right: 20rpx;
				flex-shrink: 0;
			}

			.goods-info {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				height: 120rpx;

				.goods-name {
					font-size: 28rpx;
					font-weight: 500;
					color: #333333;
					line-height: 1.4;
					margin-bottom: 8rpx;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
				}

				.goods-spec {
					font-size: 24rpx;
					color: #999999;
					margin-bottom: 8rpx;
				}

				.goods-price-qty {
					display: flex;
					justify-content: space-between;
					align-items: center;

					.goods-price {
						font-size: 28rpx;
						font-weight: 600;
						color: #f3a73f;
					}

					.goods-qty {
						font-size: 26rpx;
						color: #666666;
					}
				}
			}
		}
	}

	// 物流信息卡片
	.express-card {
		@extend .card-base;

		.express-header {
			@extend .header-base;

			.express-icon {
				@extend .icon;
			}

			.express-title {
				@extend .title;
			}
		}

		.express-info {
			padding: 20rpx 24rpx 24rpx 24rpx;
		}

		.express-item {
			display: flex;
			align-items: center;
			margin-bottom: 16rpx;

			&:last-child {
				margin-bottom: 0;
			}

			.express-label {
				font-size: 28rpx;
				color: #666666;
				width: 160rpx;
				flex-shrink: 0;
			}

			.express-value {
				font-size: 28rpx;
				color: #333333;
				font-weight: 500;
				flex: 1;
			}
		}
	}

	// 物流轨迹卡片
	.track-card {
		@extend .card-base;

		.track-header {
			@extend .header-base;

			.track-icon {
				@extend .icon;

				.cicon-event-list {
					font-size: 30rpx;
					color: #f3a73f;
				}
			}

			.track-title {
				@extend .title;
			}
		}

		.track-content {
			padding: 20rpx 24rpx 24rpx 24rpx;

			// 自定义uni-steps样式
			:deep(.uni-steps__column-text) {
				padding: 12rpx 0;
				border-bottom: 1rpx solid #f0f0f0;
				margin-bottom: 8rpx;

				&:last-child {
					border-bottom: none;
					margin-bottom: 0;
				}
			}

			:deep(.uni-steps__column-title) {
				font-size: 26rpx;
				line-height: 1.4;
				margin-bottom: 6rpx;
			}

			:deep(.uni-steps__column-desc) {
				font-size: 22rpx;
				line-height: 1.3;
				color: #999999;
			}

			:deep(.uni-steps__column-container) {
				width: 24rpx;
			}

			:deep(.uni-steps__column-circle) {
				width: 8rpx;
				height: 8rpx;
				margin: 8rpx 0 12rpx 0;
			}

			:deep(.uni-steps__column-check) {
				margin: 6rpx 0 10rpx 0;
			}

			:deep(.uni-steps__column-line--before) {
				height: 12rpx;
				transform: translate(0px, -20rpx);
			}

			:deep(.uni-steps__column-line--after) {
				transform: translate(0px, 2rpx);
			}

			// 底部展开/收缩按钮
			.track-toggle {
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 8rpx;
				padding: 16rpx 0;
				margin-top: 16rpx;
				border-top: 1rpx solid #f0f0f0;
				cursor: pointer;
				transition: background-color 0.3s ease;

				&:hover {
					background-color: #f8f8f8;
				}

				&:active {
					background-color: #f0f0f0;
				}

				.track-toggle-text {
					font-size: 24rpx;
					color: #666666;
				}

				.track-arrow {
					transition: transform 0.3s ease;

					&.track-arrow-expanded {
						transform: rotate(180deg);
					}
				}
			}
		}

		.track-empty {
			padding: 40rpx 24rpx;
		}
	}
</style>
