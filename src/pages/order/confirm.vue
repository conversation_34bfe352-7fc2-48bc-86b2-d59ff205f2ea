<template>
	<s-layout :title="state.title">
		<!-- <view v-if="state.type">
			<uni-notice-bar showIcon :text="`您的朋友（手机尾号：${state.phone}）向您赠送一份礼品，请您补全地址后领取哦。`"></uni-notice-bar>
		</view> -->
		<view v-if="state.addressShow" class="bg-white address-box ss-m-b-14 ss-r-b-10" @tap="onSelectAddress">
			<s-address-item :btnText="!state.type ? '请选择收货地址' : '请点击补全收货地址'" :item="state.addressInfo" :hasBorderBottom="false">
				<view class="ss-rest-button"><text class="_icon-forward"></text></view>
			</s-address-item>
		</view>
		<view class="order-card-box ss-m-b-14">
			<s-goods-item v-for="item in state.orderInfo.skuList" :key="item.skuId" :img="item.pic" :title="item.productName" :skuText="item.spDataValue" :price="item.price" :num="item.quantity" marginBottom="10">
				<!-- <template #top>
					<view class="order-item ss-flex ss-col-center ss-row-between ss-p-x-20 bg-white">
						<view class="item-title">配送方式</view>
						<view class="ss-flex ss-col-center">
							<text class="item-value">{{ item.dispatch_type_text }}</text>
						</view>
					</view>
				</template> -->
			</s-goods-item>
			<view class="order-item ss-flex ss-col-center ss-row-between ss-p-x-20 bg-white ss-r-10">
				<view class="item-title">付款方式</view>
				<view class="ss-flex ss-col-center">
					<uni-data-checkbox selectedColor="#ff6000" selectedTextColor="#ff6000" v-model="state.orderPayload.isPaymentType" :localdata="state.paymentType" />
				</view>
			</view>
			<!-- <view v-if="!state.type" class="order-item ss-flex ss-col-center ss-row-between ss-p-x-20 bg-white ss-r-10">
				<view class="item-title">礼品订单</view>
				<view class="ss-flex ss-col-center">
					<uni-data-checkbox @change="checkboxChange" selectedColor="#ff6000" selectedTextColor="#ff6000" v-model="state.orderPayload.isForOther" :localdata="state.isHave" />
				</view>
			</view>
			<view v-if="!state.type">
				<uni-notice-bar showIcon text="勾选"是"转为礼品订单：下单成功后到我的订单中点击"礼品订单"分享链接地址，让对方补充地址。"></uni-notice-bar>
			</view> -->
			<view class="order-item ss-flex ss-col-center ss-row-between ss-p-x-20 bg-white ss-r-10">
				<view class="item-title">订单备注</view>
				<view class="ss-flex ss-col-center">
					<uni-easyinput maxlength="20" placeholder="填写订单备注信息" v-model="state.orderPayload.remark" :inputBorder="false" :clearable="false"></uni-easyinput>
				</view>
			</view>
		</view>
		<!-- 合计 -->
		<view class="bg-white total-card-box ss-p-20 ss-m-b-14 ss-r-10">
			<view class="total-box-content border-bottom">
				<view class="order-item ss-flex ss-col-center ss-row-between">
					<view class="item-title">商品总金额</view>
					<view class="ss-flex ss-col-center">
						<text class="item-value ss-m-r-24">￥{{ state.orderInfo.productTotalAmount }}</text>
					</view>
				</view>
				<!--        <view-->
				<!--          class="order-item ss-flex ss-col-center ss-row-between"-->
				<!--          v-if="state.orderPayload.order_type === 'score'"-->
				<!--        >-->
				<!--          <view class="item-title">扣除积分</view>-->
				<!--          <view class="ss-flex ss-col-center">-->
				<!--            <image-->
				<!--              :src="sheep.$url.static('/static/img/shop/goods/score1.svg')"-->
				<!--              class="score-img"-->
				<!--            ></image>-->
				<!--            <text class="item-value ss-m-r-24">{{ state.orderInfo.score_amount }}</text>-->
				<!--          </view>-->
				<!--        </view>-->
				<view class="order-item ss-flex ss-col-center ss-row-between">
					<view class="item-title">运费</view>
					<view class="ss-flex ss-col-center">
						<text class="item-value ss-m-r-24">包邮</text>
					</view>
				</view>
				<!--        <view-->
				<!--          class="order-item ss-flex ss-col-center ss-row-between"-->
				<!--          v-if="state.orderPayload.order_type != 'score'"-->
				<!--        >-->
				<!-- <view v-if="state.orderInfo.coupon_discount_fee > 0" class="order-item ss-flex ss-col-center ss-row-between"> -->
				<!--          <view class="item-title">优惠券</view>-->
				<!--          <view class="ss-flex ss-col-center" @tap="state.showCoupon = true">-->
				<!--            <text class="item-value text-red" v-if="state.orderPayload.coupon_id"-->
				<!--              >-￥{{ state.orderInfo.coupon_discount_fee }}</text-->
				<!--            >-->
				<!--            <text-->
				<!--              class="item-value"-->
				<!--              :class="state.couponInfo.can_use?.length > 0 ? 'text-red' : 'text-disabled'"-->
				<!--              v-else-->
				<!--              >{{-->
				<!--                state.couponInfo.can_use?.length > 0-->
				<!--                  ? state.couponInfo.can_use?.length + '张可用'-->
				<!--                  : '暂无可用优惠券'-->
				<!--              }}</text-->
				<!--            >-->

				<!--            <text class="_icon-forward item-icon"></text>-->
				<!--          </view>-->
				<!--        </view>-->
				<!--        <view-->
				<!--          class="order-item ss-flex ss-col-center ss-row-between"-->
				<!--          v-if="state.orderInfo.promo_infos?.length"-->
				<!--        >-->
				<!--          &lt;!&ndash; <view v-if="state.orderInfo.promo_discount_fee > 0" class="order-item ss-flex ss-col-center ss-row-between"> &ndash;&gt;-->
				<!--          <view class="item-title">活动优惠</view>-->
				<!--          <view class="ss-flex ss-col-center" @tap="state.showDiscount = true">-->
				<!--            <text class="item-value text-red"> -￥{{ state.orderInfo.promo_discount_fee }} </text>-->
				<!--            <text class="_icon-forward item-icon"></text>-->
				<!--          </view>-->
				<!--        </view>-->
			</view>
			<view class="total-box-footer ss-font-28 ss-flex ss-row-right ss-col-center ss-m-r-28">
				<view class="total-num ss-m-r-20">共{{ state.totalNumber }}件</view>
				<view>合计：</view>
				<view class="total-num text-red"> ￥{{ state.orderInfo.orderTotalAmount }} </view>
				<view class="ss-flex" v-if="state.orderPayload.order_type === 'score'">
					<view class="total-num ss-font-30 text-red ss-m-l-4"> + </view>
					<image :src="sheep.$url.static('/static/img/shop/goods/score1.svg')" class="score-img"></image>
					<view class="total-num ss-font-30 text-red">{{ state.orderInfo.orderTotalAmount }}</view>
				</view>
			</view>
		</view>
		<!-- 发票 -->
		<!--    <view class="bg-white ss-p-20 ss-r-20">-->
		<!--      <view class="order-item ss-flex ss-col-center ss-row-between">-->
		<!--        <view class="item-title">发票申请</view>-->
		<!--        <view class="ss-flex ss-col-center" @tap="onSelectInvoice">-->
		<!--          <text class="item-value">{{ state.invoiceInfo.name || '无需开具发票' }}</text>-->
		<!--          <text class="_icon-forward item-icon"></text>-->
		<!--        </view>-->
		<!--      </view>-->
		<!--    </view>-->
		<!-- 选择优惠券弹框 -->
		<!--    <s-coupon-select-->
		<!--      v-model="state.couponInfo"-->
		<!--      :show="state.showCoupon"-->
		<!--      @confirm="onSelectCoupon"-->
		<!--      @close="state.showCoupon = false"-->
		<!--    />-->
		<!-- 满额折扣弹框  -->
		<!--    <s-discount-list-->
		<!--      v-model="state.orderInfo"-->
		<!--      :show="state.showDiscount"-->
		<!--      @close="state.showDiscount = false"-->
		<!--    />-->
		<!-- 底部 -->
		<su-fixed bottom :opacity="false" bg="bg-white" placeholder :noFixed="false" :index="200">
			<view class="footer-box border-top ss-flex ss-row-between ss-p-x-20 ss-col-center">
				<view class="total-box-footer ss-flex ss-col-center">
					<view class="total-num ss-font-30 text-red"> ￥{{ state.orderInfo.orderTotalAmount }} </view>
					<!-- <view class="total-num ss-font-30 text-red"> ￥{{ !state.type ? state.orderInfo.orderTotalAmount : '0.00' }} </view> -->
				</view>
				<button class="ss-reset-button ui-BG-Main-Gradient ss-r-40 submit-btn ui-Shadow-Main" @tap="onConfirm">
					{{ '提交订单' }}
				</button>
				<!-- <button v-if="!state.type" class="ss-reset-button ui-BG-Main-Gradient ss-r-40 submit-btn ui-Shadow-Main" @tap="onConfirm">
					{{ exchangeNow ? '立即兑换' : '提交订单' }}
				</button> -->
				<!-- 
				<button v-if="state.type == 1 && state.orderInfo.status == 6" class="ss-reset-button ui-BG-Main-Gradient ss-r-40 submit-btn ui-Shadow-Main" @tap="onSave">下单领取</button>
						<view v-if="state.type == 1 && state.orderInfo.status >= 1 && state.orderInfo.status < 6" style="font-size: 32rpx" class="text-red"> 您已领取礼物，无需再次领取 </view> 
				-->
			</view>
		</su-fixed>
	</s-layout>
</template>

<script setup>
	import { reactive, computed } from 'vue';
	import { onLoad, onPageScroll, onShow } from '@dcloudio/uni-app';
	import sheep from '@/sheep';
	import { isEmpty } from 'lodash';
	import $platform from '@/sheep/platform';
	import { showAuthModal } from '@/sheep/hooks/useModal';
	const state = reactive({
		orderPayload: {},
		orderInfo: {},
		addressInfo: {},
		invoiceInfo: {},
		totalNumber: 0,
		showCoupon: false,
		couponInfo: [],
		showDiscount: false,
		orderId: '',
		title: '确认订单',
		phone: '',
		type: '',
		isHave: [
			{
				text: '否',
				value: 0,
			},
			{
				text: '是',
				value: 1,
			},
		],
		paymentType: [
			{ text: '在线支付', value: 2 },
			{ text: '货到付款', value: 3 },
		],
		addressShow: true,
		selectAddressType: 'address',
	});

	// 立即兑换(立即兑换无需跳转收银台)
	const exchangeNow = computed(() => state.orderPayload.order_type === 'score' && state.orderInfo.pay_fee == 0);

	// 用户信息
	const userInfo = computed(() => sheep.$store('user').userInfo);

	// 跳转到我的绑定用户页面
	function handleSelectAddress(type) {
		state.selectAddressType = type;
		uni.$once('SELECT_ADDRESS', (e) => {
			console.log('🚀 ~ uni.$once ~ e.addressInfo:', e.addressInfo);
			changeConsignee(e.addressInfo);
		});
		if (type == 'bind') {
			// 来源：订单
			sheep.$router.go('/pages/user/bind/record?source=order');
		} else {
			sheep.$router.go('/pages/user/address/list');
		}
	}

	// 选择地址
	function onSelectAddress() {
		// if (state.type == 1 && !sheep.$store('user').isLogin) {
		// 	if ($platform.name === 'WechatMiniProgram') {
		// 		showAuthModal('wechatMiniLogin');
		// 	} else {
		// 		showAuthModal('smsLogin');
		// 	}
		// 	return;
		// }

		// 如果用户是接单员，则提示选择接单员地址
		if (userInfo.value.isOrderTaker == 1 && userInfo.value.orderTakerId) {
			uni.showActionSheet({
				itemList: ['我的绑定用户', '我的收货地址'],
				success: ({ tapIndex }) => {
					handleSelectAddress(tapIndex == 0 ? 'bind' : 'address');
				},
			});
			return;
		}
		uni.$once('SELECT_ADDRESS', (e) => {
			changeConsignee(e.addressInfo);
		});
		sheep.$router.go('/pages/user/address/list');
	}

	function checkboxChange(value) {
		console.log(value.detail.value);
		state.addressShow = value.detail.value == 0 ? true : false;
	}

	// 更改收货人地址&计算订单信息
	async function changeConsignee(addressInfo = {}) {
		if (isEmpty(addressInfo)) {
			const res = await sheep.$api.user.address.default();
			if (!isEmpty(res)) {
				addressInfo = res;
			}
		}
		if (!isEmpty(addressInfo)) {
			state.addressInfo = addressInfo;
			state.orderPayload.address_id = state.addressInfo.id;
		}
		if (!state.type) getOrderInfo();
	}

	// 选择优惠券
	async function onSelectCoupon(e) {
		state.orderPayload.coupon_id = e || 0;
		getOrderInfo();
		state.showCoupon = false;
	}

	// 选择发票信息
	function onSelectInvoice() {
		uni.$once('SELECT_INVOICE', (e) => {
			state.invoiceInfo = e.invoiceInfo;
			state.orderPayload.invoice_id = e.invoiceInfo.id || 0;
		});
		sheep.$router.go('/pages/user/invoice/list');
	}

	// 提交订单/立即兑换
	function onConfirm() {
		//
		if (!state.orderPayload.address_id) return sheep.$helper.toast('请选择收货地址');

		submitOrder();
		// if (exchangeNow.value) {
		//   uni.showModal({
		//     title: '提示',
		//     content: '确定使用积分立即兑换?',
		//     cancelText: '再想想',
		//     success: async function (res) {
		//       if (res.confirm) {
		//         submitOrder();
		//       }
		//     },
		//   });
		// } else {
		// }
	}

	// 创建订单&跳转
	async function submitOrder() {
		const params = {
			addressId: state.orderPayload.address_id,
			note: state.orderPayload.remark,
			from: state.orderPayload.from,
			payType: state.orderPayload.isPaymentType,
			skuList: state.orderInfo.skuList,
			isForOther: 0,
		};

		const { isOrderTaker, orderTakerId, spreadUid, isBindSpread, id } = userInfo.value;

		// 如果是接单员 并且是选择绑定用的的地址 那么传接单员会员id
		if (state.selectAddressType == 'bind' && isOrderTaker == 1 && orderTakerId) {
			params.orderTakerId = id;
			// 如果是接单员 并且是选择地址的地址 那么传接单员上级会员id
		} else if (state.selectAddressType == 'address' && spreadUid && isBindSpread == 1) {
			params.orderTakerId = spreadUid;
		}

		console.log('订单params：', params);
		uni.showLoading({
			title: '下单中...',
			mask: true,
		});
		try {
			const res = await sheep.$api.order.create(params);

			// 更新购物车列表
			if (state.orderPayload.from === 'cart') {
				sheep.$store('cart').getList();
			}

			uni.showToast({
				title: '下单成功',
				icon: 'success',
			});

			setTimeout(() => {
				// 货到付款 直接跳转支付结果页
				if (state.orderPayload.isPaymentType == 3) {
					sheep.$router.redirect('/pages/pay/result', {
						orderSN: res,
						totalAmount: state.orderInfo.orderTotalAmount,
						payType: state.orderPayload.isPaymentType,
					});
				} else {
					sheep.$router.redirect('/pages/pay/index', {
						orderSN: res,
						totalAmount: state.orderInfo.orderTotalAmount,
						orderType: 'memberConsumer',
						payType: state.orderPayload.isPaymentType,
					});
				}
			}, 800);
		} catch (error) {
			console.log(' ~ submitOrder ~ error:', error);
			sheep.$helper.toast('下单失败');
		} finally {
			uni.hideLoading();
		}
		// if (exchangeNow.value) {
		//   sheep.$router.redirect('/pages/pay/result', {
		//     orderSN: data.order_sn,
		//   });
		// } else {
		//   sheep.$router.redirect('/pages/pay/index', {
		//     orderSN: data.order_sn,
		//   });
		// }
	}
	function onSave() {
		if (state.type == 1 && !sheep.$store('user').isLogin) {
			if ($platform.name === 'WechatMiniProgram') {
				showAuthModal('wechatMiniLogin');
			} else {
				showAuthModal('smsLogin');
			}
			return;
		}
		if (!state.orderPayload.address_id) {
			sheep.$helper.toast('请选择收货地址');
			return;
		}
		const params = {
			note: state.orderPayload.remark,
			from: state.orderPayload.from,
			payType: 2,
			skuList: state.orderInfo.skuList,
		};
		params.addressId = state.orderPayload.address_id;
		params.id = state.orderId;
		console.log('保存 params：', params);
		uni.showModal({
			title: '提示',
			content: '您是否确认领取？',
			confirmText: '确认领取',
			success: async ({ confirm }) => {
				if (confirm) {
					const res = await sheep.$api.order.updateOther(params);
					uni.showToast({
						title: '领取成功',
						icon: 'success',
					});
					setTimeout(() => {
						sheep.$router.redirect('/pages/index/index');
					}, 800);
				}
			},
		});
	}

	// 检查库存&计算订单价格
	async function getOrderInfo() {
		const skuList = state.orderPayload.goods_list.map((it) => {
			return { skuId: it.skuId, quantity: it.goods_num };
		});
		try {
			let res = await sheep.$api.order.calc({ skuList });
			if (!res) {
				setTimeout(() => {
					sheep.$router.back();
				}, 2000);
				return;
			}
			state.totalNumber = 0;
			state.orderInfo = res;
			state.orderInfo.skuList.forEach((item) => {
				let str = '';
				const obj = JSON.parse(item.spData);
				Object.keys(obj).forEach((key) => {
					str += key + ': ' + obj[key] + '  ';
				});
				item.spDataValue = str;
				state.totalNumber += item.quantity;
			});
		} catch (error) {
			console.log('🚀 ~ getOrderInfo ~ error:', error);
			setTimeout(() => {
				sheep.$router.back();
			}, 2000);
		}
	}

	// 获取可用优惠券
	async function getCoupons() {
		const { error, data } = await sheep.$api.order.coupons(state.orderPayload);
		if (error === 0) {
			state.couponInfo = data;
		}
	}

	onLoad(async (options) => {
		if (options.data) {
			state.orderPayload = JSON.parse(options.data);
			changeConsignee();
			// if (state.orderPayload.order_type !== 'score') {
			//   getCoupons();
			// }
			state.orderPayload.isPaymentType = 2;
		}
	});
</script>

<style lang="scss" scoped>
	:deep() {
		.uni-input-wrapper {
			width: 320rpx;
		}

		.uni-data-checklist .checklist-group .checklist-box {
			margin-left: 50rpx;
			margin-right: 0;
		}

		.uni-easyinput__content-input {
			font-size: 28rpx;
			height: 72rpx;
			text-align: right !important;
			padding-right: 0 !important;

			.uni-input-input {
				font-weight: 500;
				color: #333333;
				font-size: 26rpx;
				height: 32rpx;
				margin-top: 4rpx;
			}
		}
		.uni-easyinput__content {
			display: flex !important;
			align-items: center !important;
			justify-content: right !important;
		}
	}
	.score-img {
		width: 36rpx;
		height: 36rpx;
		margin: 0 4rpx;
	}
	.order-item {
		height: 80rpx;

		.item-title {
			font-size: 28rpx;
			font-weight: 400;
		}

		.item-value {
			font-size: 28rpx;
			font-weight: 500;
		}

		.item-value {
			font-family: OPPOSANS;
		}
		.text-disabled {
			color: #bbbbbb;
		}

		.item-icon {
			color: $dark-9;
		}

		.remark-input {
			text-align: right;
		}

		.item-placeholder {
			color: $dark-9;
			font-size: 26rpx;
			text-align: right;
		}
	}

	.total-box-footer {
		height: 90rpx;

		.total-num {
			color: #333333;
			font-family: OPPOSANS;
		}
	}

	.footer-box {
		height: 100rpx;

		.submit-btn {
			width: 240rpx;
			height: 70rpx;
			font-size: 28rpx;
			font-weight: 500;

			.goto-pay-text {
				line-height: 28rpx;
			}
		}

		.cancel-btn {
			width: 240rpx;
			height: 80rpx;
			font-size: 26rpx;
			background-color: #e5e5e5;
			color: $dark-9;
		}
	}
	.title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
	}
	.subtitle {
		font-size: 28rpx;
		color: #999999;
	}
	.cicon-checkbox {
		font-size: 36rpx;
		color: var(--ui-BG-Main);
	}
	.cicon-box {
		font-size: 36rpx;
		color: #999999;
	}

	.address-tip-container {
		background-color: #ffffff;
		border-radius: 20rpx 20rpx 0 0;
		padding-top: 20rpx;
	}

	.address-tip-title {
		font-size: 32rpx;
		text-align: center;
		color: #333333;
		font-weight: 500;
		padding: 20rpx 0 30rpx;
	}

	.address-tip-item {
		font-size: 30rpx;
		color: #333333;
		text-align: center;
		padding: 30rpx 0;
		position: relative;
	}

	.border-bottom {
		border-bottom: 2rpx solid #f2f2f2;
	}

	.border-bottom-2 {
		border-bottom: 20rpx solid #f2f2f2;
	}
</style>
