<!-- 页面 -->
<template>
	<s-layout title="我的订单" :onShareAppMessage="state.shareInfo">
		<su-sticky bgColor="#fff">
			<su-tabs :list="tabMaps" :scrollable="false" @change="onTabsChange" :current="state.currentTab"></su-tabs>
		</su-sticky>
		<s-empty v-if="state.pagination.total === 0" icon="/static/order-empty.png" text="暂无订单"></s-empty>
		<view v-if="state.pagination.total > 0">
			<view class="bg-white order-list-card-box ss-r-10 ss-m-t-14 ss-m-20" v-for="order in state.pagination.data" :key="order.id">
				<view class="order-card-header ss-flex ss-col-center ss-row-between ss-p-x-20">
					<view class="order-no">
						<view class="order-no-text">订单号：</view>
						<view class="order-sn-value">{{ '' + order.orderSn }}</view>
					</view>
					<view class="order-state ss-font-26" :class="formatOrderColor(order.status)">
						<text>{{ getOrderStatusName(order.status) }}</text>
						<text v-if="order.aftersaleStatus > 1" class="danger-color">（{{ getOrderAfterSaleStatusName(order.aftersaleStatus) }}）</text>
					</view>
				</view>
				<view class="border-bottom" v-for="item in order.orderItemList" :key="item.id">
					<s-goods-item :img="item.pic" :title="item.productName" :skuText="item.spDataValue" :price="item.salePrice" :num="item.quantity">
						<template #tool>
							<view class="ss-flex">
								<!-- <button
									class="ss-reset-button apply-btn"
									v-if="item.btns.includes('aftersale')"
									@tap.stop="
										sheep.$router.go('/pages/order/aftersale/apply', {
											item: JSON.stringify(order),
											orderItemId: item.id,
										})
									"
								>
									申请售后
								</button> -->
								<!-- <button
									class="ss-reset-button apply-btn"
									v-if="item.btns.includes('re_aftersale')"
									@tap.stop="
										sheep.$router.go('/pages/order/aftersale/apply', {
											item: JSON.stringify(item),
										})
									"
								>
									重新售后
								</button> -->

								<!-- <button
									class="ss-reset-button apply-btn"
									v-if="item.btns.includes('aftersale_info')"
									@tap.stop="
										sheep.$router.go('/pages/order/aftersale/detail', {
											id: item.ext.aftersale_id,
										})
									"
								>
									售后详情
								</button> -->

								<button
									class="ss-reset-button apply-btn"
									v-if="item.btns.includes('buy_again')"
									@tap.stop="
										sheep.$router.go('/pages/goods/index', {
											id: item.productId,
										})
									"
								>
									再次购买
								</button>
							</view>
						</template>
					</s-goods-item>
				</view>
				<view class="pay-box ss-m-t-30 ss-flex ss-row-right ss-p-r-20">
					<view class="ss-flex ss-col-bottom ss-flex-col ss-w-100 ss-flex-gap-10" v-if="order.isDiscount == 1">
						<view class="ss-flex ss-col-center ss-m-b-10">
							<view class="discounts-title pay-color">共{{ calcGoodsCount(order.orderItemList) }}件商品， </view>
							<view class="discounts-title pay-color">原价：</view>
							<view class="original-price pay-color">￥{{ order.totalAmount.toFixed(2) }}</view>
						</view>
						<view class="ss-flex ss-col-center">
							<view class="discounts-title pay-color">折后：</view>
							<view class="discounts-money pay-color">￥{{ order.discountedPrice.toFixed(2) }}</view>
							<view class="discount-tag ss-m-l-10">已优惠￥{{ (order.totalAmount - order.discountedPrice).toFixed(2) }}</view>
						</view>
					</view>
					<view class="ss-flex ss-col-center" v-else>
						<view class="discounts-title pay-color">共{{ calcGoodsCount(order.orderItemList) }}件商品， </view>
						<view class="discounts-title pay-color">实付：</view>
						<view class="discounts-money pay-color">￥{{ order.payAmount.toFixed(2) }}</view>
					</view>
				</view>
				<view class="order-card-footer ss-flex ss-col-center ss-p-x-20">
					<!-- <su-popover>
						<button class="more-btn ss-reset-button" @click.stop>更多</button>
						<template #content>
						<view class="more-item-box">
							<view class="more-item ss-flex ss-col-center ss-reset-button">
							<view class="item-title">删除订单</view>
							</view>
							<view class="more-item ss-flex ss-col-center ss-reset-button">
							<view class="item-title">查看发票</view>
							</view>
							<view class="more-item ss-flex ss-col-center ss-reset-button">
							<view class="item-title">评价晒单</view>
							</view>
						</view>
						</template>
					</su-popover> -->
					<view class="ss-flex ss-col-center ml-auto ss-flex-gap-20">
						<!-- 
						<button open-type="share" v-if="[6].includes(order.status) && order.aftersaleStatus === 1" class="tool-btn ss-reset-button ui-BG-Main-Gradient" @tap.stop="onShare(order)">
						分享礼品
						</button>
						 -->

						<!-- 完成一个更多按钮组合的样式 -->
						<button class="apply-btn ss-reset-button" v-if="order.aftersaleStatus === 1" @tap.stop="onOrderDetail(order.orderId, 'orderId')">查看详情</button>
						<button v-if="order.status === 2 && order.aftersaleStatus === 1" class="apply-btn ss-reset-button" @tap.stop="onExpress(order.orderId)">查看物流</button>
						<button v-if="order.status === 2 && order.aftersaleStatus === 1" class="tool-btn ss-reset-button ui-BG-Main-Gradient" @tap.stop="onConfirm(order.orderId)">确认收货</button>
						<button v-if="[1, 6].includes(order.status) && order.aftersaleStatus === 1 && order.payType == 2" class="delete-btn ss-reset-button" @tap.stop="onRefund(order)">申请退款</button>
						<button v-if="order.status === 3 && order.aftersaleStatus === 1" class="apply-btn ss-reset-button" @tap.stop="onRefund(order)">申请售后</button>

						<button v-if="[1, 6].includes(order.status) && order.aftersaleStatus === 1 && order.payType == 3" class="apply-btn ss-reset-button" @tap.stop="onDeliveryCancel(order)">取消订单</button>
						<button v-if="order.status === 0" class="apply-btn ss-reset-button" @tap.stop="onCancel(order.orderId)">取消订单</button>
						<button v-if="[4, 5, 13].includes(order.status)" class="delete-btn ss-reset-button" @tap.stop="onDelete(order.orderId)">删除订单</button>
						<button v-if="order.status === 0" class="tool-btn ss-reset-button ui-BG-Main-Gradient" @tap.stop="onPay({ orderSn: order.payId, totalAmount: order.isDiscount == 1 ? order.discountedPrice : order.totalAmount })">继续支付</button>
						<button class="ss-reset-button apply-btn" v-if="order.aftersaleStatus > 1" @tap.stop="sheep.$router.go('/pages/order/aftersale/detail', { id: order.orderId })">售后详情</button>
						<button class="ss-reset-button apply-btn" v-if="[2, 5].includes(order.aftersaleStatus)" @tap.stop="cancelRefund(order.orderId)">取消售后</button>
						<button class="ss-reset-button apply-btn" v-if="order.status === 3 && order.aftersaleStatus === 1 && order.orderItemList.length == 1" @tap.stop="sheep.$router.go('/pages/goods/index', { id: order.orderItemList[0].productId })">再次购买</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 加载更多 -->
		<uni-load-more
			v-if="state.pagination.total > 0"
			:status="state.loadStatus"
			:content-text="{
				contentdown: '上拉加载更多',
			}"
			@tap="loadmore"
		/>

		<!-- 取消货到付款订单弹出框 -->
		<su-popup :show="state.showCancelDeliveryPopup" @close="state.showCancelDeliveryPopup = false" type="bottom" :round="20" :safeArea="false">
			<s-order-cancel @closePopup="state.showCancelDeliveryPopup = false" @confirmCancelDelivery="confirmCancelDelivery"></s-order-cancel>
		</su-popup>
	</s-layout>
</template>

<script setup>
	import { computed, reactive } from 'vue';
	import { onLoad, onShow, onReachBottom, onPullDownRefresh } from '@dcloudio/uni-app';
	import { formatOrderColor, getOrderStatusName, getOrderAfterSaleStatusName } from '@/sheep/hooks/useGoods';
	import sheep from '@/sheep';
	import _, { clone } from 'lodash';

	const pagination = {
		data: [],
		page: 1,
		total: 0,
		size: 5,
	};
	// 数据
	const state = reactive({
		currentTab: 0,
		pagination: {
			data: [],
			page: 1,
			total: 0,
			size: 5,
		},
		loadStatus: '',
		deleteOrderId: 0,
		error: 0,
		shareInfo: {},
		showCancelDeliveryPopup: false,
		currentCancelOrder: null,
	});

	const tabMaps = [
		{
			name: '全部',
			value: '-1',
		},
		{
			name: '待付款',
			value: '0',
		},
		{
			name: '待发货',
			value: '1',
		},
		{
			name: '待收货',
			value: '2',
		},
		{
			name: '售后单',
			value: '-2',
		},
		// {
		//   name: '礼品订单',
		//   value: '6',
		// }
	];

	const userInfo = computed(() => sheep.$store('user').userInfo);

	// function onShare(order) {
	// 	console.log(order);
	// 	state.shareInfo.title = '送您一箱酒，畅饮好时光';
	// 	state.shareInfo.image = order.orderItemList[0].pic;
	// 	// state.shareInfo.desc = "向您分享嫩水来蟹，非常好吃呦，膏肥肉美，满口留香";
	// 	state.shareInfo.path = `/pages/order/confirm?orderId=${order.orderId}&type=1&phone=${userInfo.value.phone}`;
	// 	console.log(state.shareInfo);
	// }

	// 切换选项卡
	function onTabsChange(e) {
		if (state.currentTab === e.index) return;
		state.pagination = clone(pagination);
		state.currentTab = e.index;
		state.pagination.data = [];
		getOrderList();
	}

	/**
	 * 订单详情
	 * @param {number} id 订单ID
	 */
	function onOrderDetail(id) {
		// 监听订单详情页的取消订单事件
		uni.$once('ORDER_DETAIL', (e) => {
			if (e.action == 'cancel') {
				state.pagination = clone(pagination);
				getOrderList();
			}
			if (e.action == 'confirm') {
				state.pagination = clone(pagination);
				getOrderList();
			}
			if (e.action == 'delete') {
				let index = state.pagination.data.findIndex((order) => order.orderId === e.orderId);
				state.pagination.data.splice(index, 1);
			}
		});
		sheep.$router.go('/pages/order/detail', {
			id,
		});
	}

	/**
	 * 分享拼团
	 * @param {object} order 订单
	 */
	function onOrderGroupon(order) {
		sheep.$router.go('/pages/activity/groupon/detail', {
			id: order.ext.groupon_id,
		});
	}

	/**
	 * 查看发票
	 * @param {number} invoiceId 发票ID
	 */
	function onOrderInvoice(invoiceId) {
		sheep.$router.go('/pages/order/invoice', {
			invoiceId,
		});
	}

	/**
	 * 继续支付
	 * @param {object} data 订单数据
	 */
	function onPay(data) {
		console.log('data', data);
		sheep.$router.go('/pages/pay/index', {
			orderSN: data.orderSn,
			totalAmount: data.totalAmount,
			orderType: 'memberConsumer',
		});
	}

	/**
	 * 评价
	 * @param {string} orderSN 订单号
	 */
	function onComment(orderSN) {
		sheep.$router.go('/pages/goods/comment/add', {
			orderSN,
		});
	}

	/**
	 * 确认收货
	 * @param {number} orderId 订单ID
	 */
	async function onConfirm(orderId) {
		uni.showModal({
			title: '提示',
			content: '请确认包裹全部到达后再确认收货',
			success: async function (res) {
				if (res.confirm) {
					const res = await sheep.$api.order.confirm(orderId);
					if (res) {
						sheep.$helper.toastSuccess('收货成功');
						state.pagination = clone(pagination);
						getOrderList();
					}
				}
			},
		});
	}

	// 查看物流
	async function onExpress(orderId) {
		sheep.$router.go('/pages/order/express/log', {
			orderId,
		});
	}

	// 计算商品数量
	function calcGoodsCount(data) {
		let sum = 0;
		data.forEach((it) => {
			sum += it.quantity;
		});
		return sum;
	}

	// 取消订单
	async function onCancel(orderId) {
		uni.showModal({
			title: '提示',
			content: '确定要取消订单吗?',
			success: async function (res) {
				if (res.confirm) {
					const idList = [orderId];
					const res = await sheep.$api.order.cancel({ idList });
					if (res) {
						sheep.$helper.toastSuccess('取消成功');
						state.pagination = clone(pagination);
						getOrderList();
					}
				}
			},
		});
	}

	/**
	 * 删除订单
	 * @param {number} orderId 订单ID
	 */
	function onDelete(orderId) {
		uni.showModal({
			title: '提示',
			content: '确定要删除订单吗?',
			success: async function (res) {
				if (res.confirm) {
					const res = await sheep.$api.order.delete(orderId);
					if (res == 1) {
						let index = state.pagination.data.findIndex((order) => order.orderId === orderId);
						state.pagination.data.splice(index, 1);
					}
				}
			},
		});
	}

	/**
	 * 申请退款
	 * @param {object} order 订单
	 */
	async function onRefund(order) {
		sheep.$router.go('/pages/order/aftersale/apply', { item: JSON.stringify(order) });
	}

	/**
	 * 取消货到付款订单
	 * @param {object} order 订单
	 */
	async function onDeliveryCancel(order) {
		state.showCancelDeliveryPopup = true;
		state.currentCancelOrder = order;
	}

	/**
	 * 确认取消货到付款订单
	 */
	async function confirmCancelDelivery() {
		if (!state.currentCancelOrder) return;

		const res = await sheep.$api.order.cancel({
			idList: [state.currentCancelOrder.orderId],
		});

		if (res) {
			sheep.$helper.toastSuccess('取消成功');
			state.showCancelDeliveryPopup = false;
			state.currentCancelOrder = null;
			state.pagination = clone(pagination);
			getOrderList();
		}
	}

	/**
	 * 取消售后
	 * @param {number} id 售后ID
	 */
	async function cancelRefund(id) {
		uni.showModal({
			title: '提示',
			content: '确定要取消售后吗?',
			success: async function (res) {
				if (res.confirm) {
					await sheep.$api.order.aftersale.cancel(id);
					sheep.$helper.toastSuccess('取消成功');
					reloadData();
				}
			},
		});
	}

	// 获取订单列表
	async function getOrderList() {
		state.loadStatus = 'loading';
		console.log('size:', state.pagination);
		let res = await sheep.$api.order.list({
			page: state.pagination.page - 1,
			size: state.pagination.size,
			status: tabMaps[state.currentTab].value,
		});
		const { content, totalElements, totalPages } = res;
		content.forEach((it) => {
			it.orderItemList.forEach((item) => {
				let str = '';
				const obj = JSON.parse(item.spData);
				Object.keys(obj).forEach((key) => {
					str += key + '：' + obj[key] + ' ';
				});
				// 商品规格
				item.spDataValue = str;
				item.btns = [];

				// 已完成订单显示售后按钮
				if (it.status == 3 && it.aftersaleStatus == 1 && it.payType == 2) {
					item.btns = ['aftersale'];
				}

				if (it.status == 3 && it.aftersaleStatus == 1 && it.orderItemList.length > 1) {
					item.btns = ['buy_again'];
				}
			});
		});
		state.pagination.data = _.concat(state.pagination.data, content);
		state.pagination.total = totalElements;
		if (state.pagination.page < totalPages) {
			state.loadStatus = 'more';
		} else {
			state.loadStatus = 'noMore';
		}
	}

	onLoad(async (options) => {
		if (options.type) {
			state.currentTab = options.type;
		}
		getOrderList();
	});

	// 加载更多
	function loadmore() {
		if (state.loadStatus !== 'noMore') {
			state.pagination.page++;
			getOrderList();
		}
	}

	// 上拉加载更多
	onReachBottom(() => {
		loadmore();
	});

	//下拉刷新
	onPullDownRefresh(() => {
		state.pagination = clone(pagination);
		getOrderList();
		setTimeout(function () {
			uni.stopPullDownRefresh();
		}, 800);
	});

	function reloadData() {
		state.pagination = clone(pagination);
		getOrderList();
	}
</script>

<style lang="scss" scoped>
	.score-img {
		width: 36rpx;
		height: 36rpx;
		margin: 0 4rpx;
	}
	.tool-btn {
		width: 160rpx;
		height: 55rpx;
		background: white;
		font-size: 26rpx;
		border-radius: 30rpx;
		margin-right: 10rpx;

		&:last-of-type {
			margin-right: 0;
		}
	}

	.delete-btn {
		width: 160rpx;
		height: 55rpx;
		color: #ff3000;
		background: #fee;
		border-radius: 30rpx;
		font-size: 26rpx;
		margin-right: 10rpx;
		line-height: normal;
		border: 1rpx solid #ff3000;

		&:last-of-type {
			margin-right: 0;
		}
	}

	.apply-btn {
		width: 160rpx;
		height: 55rpx;
		border-radius: 30rpx;
		font-size: 26rpx;
		border: 2rpx solid #dcdcdc;
		line-height: normal;

		&:last-of-type {
			margin-right: 0;
		}
	}

	.swiper-box {
		flex: 1;

		.swiper-item {
			height: 100%;
			width: 100%;
		}
	}
	.order-list-card-box {
		transition: all 0.3s ease;
		border-top-left-radius: 10rpx;
		border-top-right-radius: 10rpx;

		&:active {
			box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
		}

		.order-card-header {
			position: relative;
			height: 120rpx;
			background: linear-gradient(135deg, #fff8f0 0%, #ffffff 100%);
			border-bottom: 1rpx solid #f0f0f0;
			overflow: hidden;
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.02);
			border-top-left-radius: 10rpx;
			border-top-right-radius: 10rpx;

			// 装饰性背景元素
			&::before {
				content: '';
				position: absolute;
				top: -20rpx;
				right: -40rpx;
				width: 120rpx;
				height: 120rpx;
				background: linear-gradient(135deg, $orange 0%, rgba($orange, 0.1) 100%);
				border-radius: 50%;
				opacity: 0.12;
				z-index: 1;
			}

			&::after {
				content: '';
				position: absolute;
				bottom: -30rpx;
				left: -30rpx;
				width: 80rpx;
				height: 80rpx;
				background: linear-gradient(45deg, $orange 0%, rgba($orange, 0.2) 100%);
				border-radius: 50%;
				opacity: 0.08;
				z-index: 1;
			}

			.order-no {
				z-index: 2;
				font-size: 28rpx;
				font-weight: 600;
				color: $dark-3;
				display: flex;
				align-items: flex-start;
				flex-direction: column;
				justify-content: center;
				gap: 10rpx;

				.order-no-text {
					position: relative;
					display: flex;
					align-items: center;
					font-weight: 700;
					color: #333;
					padding-left: 20rpx;
					&::before {
						content: '';
						position: absolute;
						left: 0;
						top: 50%;
						transform: translateY(-50%);
						width: 6rpx;
						height: 28rpx;
						background: linear-gradient(135deg, $orange 0%, #ff8533 100%);
						border-radius: 3rpx;
						flex-shrink: 0;
					}
				}

				.order-sn-value {
					font-size: 26rpx;
					color: #666;
				}
			}

			.order-state {
				position: relative;
				z-index: 2;
				font-size: 28rpx;
				font-weight: 600;
				padding: 0;
				text-align: right;
				white-space: nowrap;
				flex-shrink: 0;
				border-radius: 8rpx;
			}
		}

		.pay-box {
			.discounts-title {
				font-size: 24rpx;
				line-height: normal;
				color: #999999;
			}

			.discounts-money {
				font-size: 24rpx;
				line-height: normal;
				color: #999;
				font-family: OPPOSANS;
			}

			.pay-color {
				color: #333;
			}
		}

		.order-card-footer {
			height: 100rpx;

			.more-item-box {
				padding: 20rpx;

				.more-item {
					height: 60rpx;

					.title {
						font-size: 26rpx;
					}
				}
			}

			.more-btn {
				color: $dark-9;
				font-size: 24rpx;
			}

			.content {
				width: 154rpx;
				color: #333333;
				font-size: 26rpx;
				font-weight: 500;
			}
		}
	}

	:deep(.uni-tooltip-popup) {
		background: var(--ui-BG);
	}

	// 订单状态颜色优化 - 纯文字版本
	.warning-color {
		color: #f3a73f !important;
		font-weight: 700 !important;
	}

	.danger-color {
		color: #ff4d4f !important;
		font-weight: 700 !important;
	}

	.success-color {
		color: #52c41a !important;
		font-weight: 700 !important;
	}

	.info-color {
		color: #999999 !important;
		font-weight: 700 !important;
	}

	.original-price {
		font-size: 24rpx;
		line-height: normal;
		color: #999;
		font-family: OPPOSANS;
		text-decoration: line-through;
	}

	.discount-tag {
		font-size: 22rpx;
		line-height: normal;
		color: #ffffff;
		background-color: #ff6b00;
		padding: 4rpx 10rpx;
		border-radius: 8rpx;
	}
</style>
