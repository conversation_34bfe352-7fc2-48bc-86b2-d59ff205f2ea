<template>
  <s-layout class="set-wrap" :title="state.title" :bgStyle="{ color: '#FFF' }">
    <view class="ss-p-30"><mp-html class="richtext" :content="state.content"></mp-html></view>
  </s-layout>
</template>

<script setup>
import { onLoad } from '@dcloudio/uni-app';
import { reactive } from 'vue';
import sheep from '@/sheep';
import $platform from '@/sheep/platform';
import { showAuthModal } from '@/sheep/hooks/useModal';

const state = reactive({
  title: '',
  content: '',
  key: ''
});

async function getRichTextContent(key) {
  // const { error, data } = await sheep.$api.data.richtext(id);
  const res = await sheep.$api.data.richtextOther(key);
  if (res.code == 200) {
    state.content = res.data.remark;
    if (state.title === '') {
      state.title = res.data.configName;
      uni.setNavigationBarTitle({
        title: state.title,
      });
    }
  }
}
onLoad((options) => {
  if (options.title) {
    state.title = options.title;
    uni.setNavigationBarTitle({
      title: state.title,
    });
  }
  getRichTextContent(options.value);
});


</script>

<style lang="scss" scoped>
.set-title {
  margin: 0 30rpx;
}

.richtext {
}
</style>
