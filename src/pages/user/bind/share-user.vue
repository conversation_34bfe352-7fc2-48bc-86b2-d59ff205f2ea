<template>
	<s-layout title="绑定邀请码" navbar="normal">
		<view class="bind-page">
			<view class="card-container">
				<view class="card-header">
					<view class="header-line"></view>
					<text class="header-title">绑定分享邀请码</text>
				</view>

				<view class="card-content">
					<view class="avatar-section">
						<view class="avatar-wrapper">
							<image class="avatar-image" src="/static/user/default_avatar.png" mode="aspectFill"></image>
						</view>
						<text class="sharer-name">来自 {{ inviteNickname }} 的邀请</text>
					</view>

					<view class="code-display">
						<text class="code-label">邀请码</text>
						<view class="code-value-box">
							<text v-for="(digit, index) in displayCode" :key="index" class="code-digit">{{ digit }}</text>
						</view>
						<text class="code-tip">该邀请码已自动填入，点击下方按钮即可绑定</text>
					</view>

					<view class="benefits-section">
						<view class="benefit-item">
							<view class="benefit-icon">
								<uni-icons type="gift" size="24" color="#FF6000"></uni-icons>
							</view>
							<text class="benefit-text">专属接单员一对一服务，帮您便捷高效下单</text>
						</view>
						<view class="benefit-item">
							<view class="benefit-icon">
								<uni-icons type="gift" size="24" color="#FF6000"></uni-icons>
							</view>
							<text class="benefit-text">尊享VIP专属优惠，独家折扣礼遇</text>
						</view>
						<view class="benefit-item">
							<view class="benefit-icon">
								<uni-icons type="gift" size="24" color="#FF6000"></uni-icons>
							</view>
							<text class="benefit-text">限时活动优先参与权，抢先体验新功能</text>
						</view>
					</view>
				</view>
			</view>

			<view class="footer-box">
				<button class="bind-btn" @tap="bindInviteCode">立即绑定</button>
			</view>
		</view>
	</s-layout>
</template>

<script setup>
	import { ref, computed, onMounted } from 'vue';
	import { onLoad, onShow } from '@dcloudio/uni-app';
	import { showAuthModal } from '@/sheep/hooks/useModal';
	import sheep from '@/sheep';

	// 邀请码 - 通常从路由参数中获取
	const inviteCode = ref('');

	// 邀请人昵称
	const inviteNickname = ref('');

	// 分享人id
	const shareUserId = ref('');

	// 用于显示的邀请码（分割为单个数字）
	const displayCode = computed(() => {
		return inviteCode.value.split('');
	});

	// 从页面参数中获取邀请码
	onLoad((options) => {
		console.log('🚀 ~ onLoad ~ options:', options);
		if (options) {
			inviteCode.value = options.c;
			inviteNickname.value = options.n;
			shareUserId.value = options.u;
		}
	});

	// 绑定邀请码
	const bindInviteCode = () => {
		// 必须登录
		if (!sheep.$store('user').isLogin) return showAuthModal('wechatMiniLogin');

		// 邀请码不能为空
		if (!inviteCode.value) return sheep.$helper.toast('邀请码不能为空');

		uni.showModal({
			title: '提示',
			content: '绑定后接单员可帮您快速下单，方便快捷',
			confirmText: '立即绑定',
			cancelText: '暂不绑定',
			cancelColor: '#999999',
			confirmColor: '#FF6000',
			success: async ({ confirm }) => {
				if (confirm) {
					const result = await sheep.$api.user.bindShareUser(shareUserId.value);
					console.log('🚀 ~ bindInviteCode ~ result:', result);
					if (result.code === 200) {
						sheep.$helper.toastSuccess('绑定成功');
						setTimeout(() => {
							sheep.$router.go('/pages/index/index');
						}, 1000);
					}
				}
			},
		});
	};

	onShow(() => {
		// 未登录优先登录
		if (!sheep.$store('user').isLogin) {
			showAuthModal('wechatMiniLogin');
		}
	});
</script>

<style lang="scss" scoped>
	.bind-page {
		background-color: var(--ui-BG-1);
		padding: 30rpx;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
	}

	.card-container {
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
	}

	.card-header {
		display: flex;
		align-items: center;
		margin-bottom: 40rpx;

		.header-line {
			width: 6rpx;
			height: 30rpx;
			background: linear-gradient(180deg, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
			border-radius: 3rpx;
			margin-right: 16rpx;
		}

		.header-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333333;
		}
	}

	.avatar-section {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-bottom: 30rpx;

		.avatar-wrapper {
			width: 140rpx;
			height: 140rpx;
			border-radius: 50%;
			overflow: hidden;
			margin-bottom: 20rpx;
			box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
			border: 4rpx solid rgba(var(--ui-BG-Main-rgb), 0.2);

			.avatar-image {
				width: 100%;
				height: 100%;
			}
		}

		.sharer-name {
			font-size: 30rpx;
			color: #333333;
			font-weight: 500;
		}
	}

	.code-display {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin: 20rpx 0 40rpx;

		.code-label {
			font-size: 28rpx;
			color: #999999;
			margin-bottom: 20rpx;
		}

		.code-value-box {
			display: flex;
			justify-content: center;
			margin-bottom: 20rpx;
		}

		.code-digit {
			width: 70rpx;
			height: 90rpx;
			background: linear-gradient(135deg, #f8f8f8, #efefef);
			margin: 0 8rpx;
			border-radius: 12rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 42rpx;
			font-weight: bold;
			color: var(--ui-BG-Main);
			box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
		}

		.code-tip {
			font-size: 24rpx;
			color: #999999;
			margin-top: 20rpx;
		}
	}

	.benefits-section {
		background-color: #f8f9fa;
		border-radius: 12rpx;
		padding: 24rpx;

		.benefit-item {
			display: flex;
			align-items: center;
			margin-bottom: 16rpx;

			&:last-child {
				margin-bottom: 0;
			}

			.benefit-icon {
				width: 50rpx;
				height: 50rpx;
				border-radius: 25rpx;
				background-color: rgba(var(--ui-BG-Main-rgb), 0.1);
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 16rpx;
				font-size: 28rpx;
			}

			.benefit-text {
				font-size: 26rpx;
				color: #666666;
			}
		}
	}

	.footer-box {
		margin-top: auto;
		padding: 40rpx 0;

		.bind-btn {
			width: 100%;
			height: 90rpx;
			line-height: 90rpx;
			background: linear-gradient(90deg, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
			border-radius: 45rpx;
			color: #ffffff;
			font-size: 32rpx;
			font-weight: 500;
		}
	}
</style>
