<template>
	<s-layout title="绑定记录" navbar="normal">
		<view class="page-container">
			<uni-search-bar v-model="searchVal" bgColor="#fff" placeholder="请输入手机号后四位进行查询" @confirm="handleSearch" @clear="handleClear" @cancel="handleCancel" />
			<view class="record-container">
				<view v-if="list.length === 0" class="empty-records">
					<s-empty icon="/static/data-empty.png" text="暂无数据"></s-empty>
				</view>
				<view v-else class="record-list">
					<view v-for="(item, index) in list" :key="index" class="record-item">
						<view class="record-item-content">
							<view class="user-info-section">
								<view class="user-avatar">{{ item.nickname.substring(0, 1) }}</view>
								<view class="user-info">
									<text class="nickname">{{ item.nickname }}</text>
									<text class="phone">{{ formatPhone(item.phoneHidden) }}</text>
								</view>
							</view>
							<view class="action-section">
								<view class="bind-time-box">
									<text class="bind-time">{{ sheep.$helper.timeFormat(new Date(item.spreadTime), 'yyyy-MM-dd hh:mm:ss') }}</text>
								</view>
							</view>
						</view>
						<button v-if="isFromOrder" class="select-address-btn" @tap="handleSelectAddress(item)">选择收货地址</button>
					</view>
				</view>
			</view>
		</view>
		<su-popup :show="showAddress" @close="showAddress = false">
			<view class="address-popup-container">
				<view class="address-popup-header">
					<text class="address-popup-title">收货地址列表</text>
					<view class="address-popup-close" @tap="showAddress = false">
						<uni-icons type="close" size="20"></uni-icons>
					</view>
				</view>
				<scroll-view scroll-y class="address-list-scroll">
					<view v-if="addressList.length === 0" class="empty-address">
						<s-empty icon="/static/data-empty.png" text="暂无地址数据"></s-empty>
					</view>
					<view v-else class="address-list">
						<view v-for="(address, index) in addressList" :key="index" class="address-item" @tap="selectUserAddress(address)">
							<view class="address-info">
								<view class="address-user-info">
									<view class="user-avatar">{{ address.name.substring(0, 1) }}</view>
									<view class="user-details">
										<view class="name-phone-row">
											<text class="address-name">{{ address.name }}</text>
											<text class="address-phone">{{ address.phone }}</text>
										</view>
									</view>
								</view>
								<view class="address-location">
									<view class="location-icon">
										<uni-icons type="location" size="20"></uni-icons>
									</view>
									<view class="location-details">
										<text class="address-area">{{ address.province }} {{ address.city }} {{ address.district }}</text>
										<text class="address-detail">{{ address.detailAddress }}</text>
									</view>
								</view>
								<view class="select-btn">选择此地址</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</su-popup>
	</s-layout>
</template>

<script setup>
	import { reactive, ref, computed } from 'vue';
	import { onLoad } from '@dcloudio/uni-app';
	import sheep from '@/sheep';

	// 绑定记录列表
	const list = ref([]);

	// 来源
	const source = ref('');

	// 是否显示地址选择弹窗
	const showAddress = ref(false);

	// 地址列表
	const addressList = ref([]);

	// 搜索框数据
	const searchVal = ref('');

	// 判断是否来自订单页面
	const isFromOrder = computed(() => source.value === 'order');

	/**
	 * 格式化手机号
	 * @param {String} phone 手机号
	 * @returns {String} 格式化后的手机号
	 */
	const formatPhone = (phone) => {
		return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
	};

	/**
	 * 选择用户地址
	 * @param {Object} addressInfo 地址信息
	 */
	const selectUserAddress = (addressInfo) => {
		console.log('选择的地址:', addressInfo);
		// 创建地址信息对象
		// 触发选择地址事件并返回到订单页
		uni.$emit('SELECT_ADDRESS', { addressInfo });
		showAddress.value = false;
		sheep.$router.back();
	};

	/**
	 * 选择用户地址
	 * @param {Object} user 用户信息
	 */
	const handleSelectAddress = (user) => {
		console.log('🚀 ~ handleSelectAddress ~ user:', user);
		// 尝试从API获取地址，如果失败则使用模拟数据
		sheep.$api.user.address
			.listByMemberId(user.id)
			.then((res) => {
				if (res && res.length > 0) {
					addressList.value = res;
				}
				showAddress.value = true;
			})
			.catch(() => {
				showAddress.value = true;
			});
	};

	// 获取我的绑定记录
	const getMyQrCodeRecord = async () => {
		const res = await sheep.$api.user.getMyQrCodeRecord(searchVal.value);
		if (res.code == 200) {
			list.value = res.data;
		}
	};

	const handleSearch = () => {
		getMyQrCodeRecord();
	};

	onLoad((options) => {
		// 来源
		source.value = options.source;
		// 获取我的绑定记录
		getMyQrCodeRecord();
	});
</script>

<style lang="scss" scoped>
	.page-container {
		padding: 30rpx;
		background-color: var(--ui-BG-1);
		min-height: 100vh;
	}

	:deep(.uni-searchbar__box) {
		height: 80rpx !important;
	}

	.record-container {
		margin-top: 20rpx;
	}

	.record-header {
		display: flex;
		align-items: center;
		padding-bottom: 24rpx;
		border-bottom: 1rpx solid #f0f0f0;
		margin-bottom: 24rpx;

		.line {
			width: 6rpx;
			height: 28rpx;
			background: linear-gradient(180deg, var(--ui-BG-Main) 0%, var(--ui-BG-Main-gradient) 100%);
			border-radius: 3rpx;
			margin-right: 12rpx;
		}

		.header-title {
			font-size: 30rpx;
			font-weight: bold;
			color: var(--ui-TC);
			letter-spacing: 0.5rpx;
		}
	}

	.empty-records {
		padding: 80rpx 0;
		text-align: center;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.record-list {
		.record-item {
			display: flex;
			flex-direction: column;
			margin-bottom: 24rpx;
			border-radius: 12rpx;
			overflow: hidden;
			background-color: #fff;
			&:last-child {
				margin-bottom: 0;
			}

			.record-item-content {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 20rpx 16rpx;
				border-radius: 12rpx 12rpx 0 0;
				box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.03);
			}

			.user-info-section {
				display: flex;
				align-items: center;
			}

			.user-avatar {
				width: 64rpx;
				height: 64rpx;
				border-radius: 50%;
				background: linear-gradient(135deg, var(--ui-BG-Main-light) 0%, var(--ui-BG-Main) 100%);
				color: #ffffff;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 26rpx;
				font-weight: bold;
				margin-right: 16rpx;
				box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
			}

			.user-info {
				display: flex;
				flex-direction: column;

				.nickname {
					font-size: 26rpx;
					color: var(--ui-TC);
					margin-bottom: 4rpx;
					font-weight: 500;
				}

				.phone {
					font-size: 22rpx;
					color: var(--ui-TC-3);
				}
			}

			.action-section {
				display: flex;
				flex-direction: column;
				align-items: flex-end;
			}

			.bind-time-box {
				padding: 4rpx 12rpx;
				background-color: rgba(var(--ui-BG-Main-rgb), 0.1);
				border-radius: 12rpx;

				.bind-time {
					font-size: 20rpx;
					color: var(--ui-BG-Main);
					font-weight: 500;
				}
			}
		}
	}

	.select-address-btn {
		padding: 6rpx 0;
		background-color: var(--ui-BG-Main);
		color: #ffffff;
		font-size: 22rpx;
		border-radius: 0;
		border: none;
		line-height: 2;
		min-height: auto;
		width: 100%;
		text-align: center;
		font-weight: 500;
		letter-spacing: 1rpx;
		box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
		transition: all 0.2s ease;

		&:active {
			opacity: 0.9;
		}
	}

	.address-popup-container {
		background-color: #ffffff;
		border-radius: 24rpx 24rpx 0 0;
		max-height: 75vh;
		display: flex;
		flex-direction: column;
		overflow: hidden;
		box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);
	}

	.address-popup-header {
		padding: 30rpx 30rpx;
		border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
		display: flex;
		justify-content: space-between;
		align-items: center;
		position: relative;
	}

	.address-popup-title {
		font-size: 34rpx;
		font-weight: 600;
		color: var(--ui-TC);
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
	}

	.address-popup-close {
		font-size: 44rpx;
		color: var(--ui-TC-3);
		height: 60rpx;
		width: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1;
	}

	.address-list-scroll {
		max-height: calc(75vh - 108rpx);
		padding: 10rpx 0;
	}

	.empty-address {
		padding: 80rpx 0;
		text-align: center;
	}

	.address-list {
		padding: 0 30rpx;
	}

	.address-item {
		padding: 30rpx 0;
		border-bottom: 1rpx solid rgba(0, 0, 0, 0.04);
		position: relative;

		&:last-child {
			border-bottom: none;
		}

		&:active {
			background-color: rgba(0, 0, 0, 0.02);
		}
	}

	.address-info {
		display: flex;
		flex-direction: column;
	}

	.address-user-info {
		display: flex;
		align-items: center;
		margin-bottom: 24rpx;
	}

	.user-avatar {
		width: 70rpx;
		height: 70rpx;
		border-radius: 50%;
		background: linear-gradient(135deg, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
		color: #fff;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 32rpx;
		font-weight: 500;
		margin-right: 20rpx;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.user-details {
		flex: 1;
	}

	.name-phone-row {
		display: flex;
		align-items: center;
		margin-bottom: 8rpx;
	}

	.address-name {
		font-size: 30rpx;
		font-weight: 600;
		color: var(--ui-TC);
		margin-right: 16rpx;
	}

	.address-phone {
		font-size: 26rpx;
		color: var(--ui-TC-2);
	}

	.tag-row {
		display: flex;
		align-items: center;
	}

	.default-tag {
		font-size: 20rpx;
		color: #ffffff;
		background: linear-gradient(90deg, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
		padding: 2rpx 12rpx;
		border-radius: 10rpx;
		margin-right: 12rpx;
	}

	.address-type-tag {
		font-size: 20rpx;
		color: var(--ui-TC-2);
		background-color: rgba(0, 0, 0, 0.04);
		padding: 2rpx 12rpx;
		border-radius: 10rpx;
	}

	.address-location {
		display: flex;
		align-items: flex-start;
		background-color: rgba(0, 0, 0, 0.02);
		padding: 20rpx;
		border-radius: 16rpx;
	}

	.location-icon {
		font-size: 32rpx;
		margin-right: 12rpx;
		line-height: 1.2;
	}

	.location-details {
		flex: 1;
		display: flex;
		flex-direction: column;
	}

	.address-area {
		font-size: 26rpx;
		color: var(--ui-TC-1);
		margin-bottom: 6rpx;
		font-weight: 500;
	}

	.address-detail {
		font-size: 26rpx;
		color: var(--ui-TC-2);
		line-height: 1.5;
	}

	.select-btn {
		margin-top: 20rpx;
		background: linear-gradient(90deg, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
		color: #fff;
		font-size: 28rpx;
		font-weight: 500;
		text-align: center;
		padding: 16rpx 0;
		border-radius: 40rpx;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
	}
</style>
