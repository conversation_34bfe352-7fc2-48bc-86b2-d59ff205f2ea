<template>
	<s-layout title="我的接单员" navbar="normal">
		<view class="page-container">
			<view class="my-order-taker-container">
				<!-- 头部区域 -->
				<view class="header-section">
					<view class="header-bg"></view>
					<view class="header-content">
						<view class="header-title">
							<text class="title-text">我的接单员</text>
							<view class="title-line"></view>
						</view>
					</view>
				</view>

				<!-- 接单员信息卡片 -->
				<view class="order-taker-card" v-if="orderTaker">
					<!-- 背景装饰元素 -->
					<view class="card-decoration">
						<view class="decoration-circle circle-1"></view>
						<view class="decoration-circle circle-2"></view>
						<view class="decoration-line line-1"></view>
						<view class="decoration-line line-2"></view>
					</view>

					<!-- 卡片内容 -->
					<view class="card-content">
						<!-- 头部信息 -->
						<view class="card-header ss-flex ss-col-center ss-row-between">
							<view class="ss-flex ss-col-center">
								<view class="avatar-container">
									<image class="avatar" src="/static/user/default_avatar.png" mode="aspectFill"></image>
									<view class="avatar-badge"></view>
								</view>
								<view class="user-info">
									<view class="name-area">
										<text class="nickname">{{ orderTaker.nickname || '暂无昵称' }}</text>
									</view>
								</view>
							</view>
							<view class="status-box">
								<view class="status-dot"></view>
								<text class="status-text">已绑定</text>
							</view>
						</view>

						<!-- 信息展示 -->
						<view class="card-body">
							<view class="info-divider">
								<view class="divider-line"></view>
								<text class="divider-text">接单员信息</text>
								<view class="divider-line"></view>
							</view>

							<view class="info-grid">
								<view class="info-grid-item">
									<view class="info-icon-container">
										<uni-icons type="phone" size="18" color="var(--ui-BG-Main)"></uni-icons>
									</view>
									<text class="info-label">联系方式</text>
									<text class="info-value">{{ orderTaker.phoneHidden || '暂无' }}</text>
								</view>
								<view class="info-grid-item">
									<view class="info-icon-container">
										<uni-icons type="star" size="18" color="var(--ui-BG-Main)"></uni-icons>
									</view>
									<text class="info-label">服务评分</text>
									<text class="info-value">5.0</text>
								</view>
							</view>
						</view>

						<!-- 底部按钮 -->
						<view class="card-footer">
							<button class="action-btn contact-btn" @click="callPhone()">
								<uni-icons type="chatbubble" size="16" color="#FFFFFF"></uni-icons>
								<text class="btn-text">联系接单员</text>
							</button>
							<button class="action-btn unbind-btn" @click="showUnbindModal = true">
								<uni-icons type="close" size="16" color="#ff4d4f"></uni-icons>
								<text class="btn-text">解绑接单员</text>
							</button>
						</view>
					</view>
				</view>

				<!-- 无接单员状态 -->
				<view class="empty-state" v-else>
					<s-empty text="您还未绑定接单员" icon="/static/internet-empty.png" />
					<text class="empty-desc">绑定接单员可享受专属服务</text>
					<!-- <button class="bind-btn ui-BG-Main-Gradient">立即绑定接单员</button> -->
				</view>

				<!-- 接单员说明 -->
				<view class="info-section">
					<view class="section-title">
						<view class="title-icon"></view>
						<text class="title-text">接单员说明</text>
					</view>
					<view class="info-content">
						<view class="info-item ss-flex">
							<view class="info-icon">
								<uni-icons type="person" size="18" color="var(--ui-BG-Main)"></uni-icons>
							</view>
							<text class="info-text">接单员为您提供下单等专属服务</text>
						</view>
						<view class="info-item ss-flex">
							<view class="info-icon">
								<uni-icons type="medal" size="18" color="var(--ui-BG-Main)"></uni-icons>
							</view>
							<text class="info-text">接单员将为您提供专属优惠</text>
						</view>
					</view>
				</view>
			</view>
			<!-- 解绑确认弹窗 -->
			<view class="unbind-modal" v-if="showUnbindModal" @tap="closeModal">
				<view class="modal-content" @tap.stop>
					<view class="modal-header">
						<view class="warning-icon">
							<uni-icons type="info-filled" size="32" color="#ff4d4f"></uni-icons>
						</view>
						<text class="modal-title">确认解绑接单员？</text>
					</view>

					<view class="modal-body">
						<text class="modal-desc">解绑后将失去以下权益：</text>
						<view class="privilege-list">
							<view class="privilege-item">
								<view class="privilege-dot"></view>
								<text class="privilege-text">专属接单员服务</text>
							</view>
							<view class="privilege-item">
								<view class="privilege-dot"></view>
								<text class="privilege-text">专属优惠和折扣</text>
							</view>
							<view class="privilege-item">
								<view class="privilege-dot"></view>
								<text class="privilege-text">优先处理订单</text>
							</view>
						</view>
						<text class="modal-warning">解绑后可重新绑定其他接单员</text>
					</view>

					<view class="modal-footer">
						<button class="modal-btn cancel-btn" @click="closeModal">
							<text>取消</text>
						</button>
						<button class="modal-btn confirm-btn" @click="confirmUnbind">
							<text>确认解绑</text>
						</button>
					</view>
				</view>
			</view>
		</view>
	</s-layout>
</template>

<script setup>
	import { ref, computed } from 'vue';
	import { onLoad } from '@dcloudio/uni-app';
	import sheep from '@/sheep';

	// 数据状态
	const orderTaker = ref(null);

	// 是否显示解绑弹窗
	const showUnbindModal = ref(false);

	const userInfo = computed(() => sheep.$store('user').userInfo);

	// 拨打电话
	const callPhone = async () => {
		console.log('🚀 ~ callPhone ~ orderTaker.value:', sheep);
		if (orderTaker.value.phoneEncrypted) {
			// 解密手机号
			const res = await sheep.$api.user.decryptPhone(orderTaker.value.phoneEncrypted);
			// 拨打电话
			if (res) sheep.$helper.tools.callPhone(res);
		} else sheep.$helper.toast('暂无联系方式');
	};

	// 关闭弹窗
	const closeModal = () => {
		showUnbindModal.value = false;
	};

	// 确认解绑
	const confirmUnbind = async () => {
		try {
			// 显示加载状态
			uni.showLoading({
				title: '解绑中...',
			});

			const params = { id: userInfo.value.id, spreadUid: null };

			// 调用解绑API
			const { code, msg } = await sheep.$api.user.unbindOrderTaker(params);

			uni.hideLoading();

			if (code === 200) {
				sheep.$helper.toastSuccess('解绑成功');

				closeModal();

				setTimeout(() => {
					sheep.$router.go('/pages/index/user');
				}, 1000);
			} else {
				sheep.$helper.toast(msg || '解绑失败');
			}
		} catch (error) {
			uni.hideLoading();
			console.error('解绑接单员失败:', error);
			sheep.$helper.toast('解绑失败，请重试');
		}
	};

	// 获取接单员信息
	const getOrderTakerInfo = async () => {
		const { code, data } = await sheep.$api.user.getOrderTakerInfo();
		if (code == 200) orderTaker.value = data;
	};

	onLoad(() => {
		getOrderTakerInfo();
	});
</script>

<style lang="scss" scoped>
	.page-container {
		min-height: 100vh;
		background-color: #f6f6f6;
		padding-bottom: 40rpx;
	}

	.my-order-taker-container {
		padding: 0 30rpx;
	}

	/* 头部区域 */
	.header-section {
		position: relative;
		height: 180rpx;
		margin-bottom: 30rpx;
		overflow: hidden;

		.header-bg {
			position: absolute;
			top: 0;
			left: -30rpx;
			right: -30rpx;
			height: 180rpx;
			background: linear-gradient(90deg, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
			border-radius: 0 0 50% 50% / 0 0 25% 25%;
		}

		.header-content {
			position: relative;
			z-index: 1;
			display: flex;
			align-items: center;
			justify-content: center;
			height: 100%;
		}

		.header-title {
			display: flex;
			flex-direction: column;
			align-items: center;

			.title-text {
				font-size: 36rpx;
				font-weight: bold;
				color: #ffffff;
				margin-bottom: 15rpx;
			}

			.title-line {
				width: 60rpx;
				height: 6rpx;
				background-color: #ffffff;
				border-radius: 3rpx;
			}
		}
	}

	/* 接单员卡片 */
	.order-taker-card {
		position: relative;
		background-color: #ffffff;
		border-radius: 20rpx;
		box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
		padding: 0;
		margin-bottom: 30rpx;
		overflow: hidden;

		/* 装饰元素 */
		.card-decoration {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			pointer-events: none;
			z-index: 1;
			overflow: hidden;

			.decoration-circle {
				position: absolute;
				border-radius: 50%;
				opacity: 0.05;
			}

			.circle-1 {
				top: -100rpx;
				right: -100rpx;
				width: 300rpx;
				height: 300rpx;
				background: var(--ui-BG-Main);
			}

			.circle-2 {
				bottom: -80rpx;
				left: -80rpx;
				width: 200rpx;
				height: 200rpx;
				background: var(--ui-BG-Main);
			}

			.decoration-line {
				position: absolute;
				background: var(--ui-BG-Main);
				opacity: 0.05;
			}

			.line-1 {
				top: 60rpx;
				left: 100rpx;
				width: 100rpx;
				height: 4rpx;
				transform: rotate(45deg);
			}

			.line-2 {
				bottom: 120rpx;
				right: 80rpx;
				width: 120rpx;
				height: 4rpx;
				transform: rotate(-30deg);
			}
		}

		/* 卡片内容 */
		.card-content {
			position: relative;
			z-index: 2;
			padding: 40rpx 30rpx;
		}

		.card-header {
			margin-bottom: 40rpx;

			.avatar-container {
				position: relative;
				width: 120rpx;
				height: 120rpx;

				.avatar {
					width: 120rpx;
					height: 120rpx;
					border-radius: 60rpx;
					border: 4rpx solid rgba(var(--ui-BG-Main), 0.2);
					background-color: #f5f5f5;
				}

				.avatar-badge {
					position: absolute;
					right: 0;
					bottom: 0;
					width: 28rpx;
					height: 28rpx;
					border-radius: 50%;
					background: var(--ui-BG-Main);
					border: 4rpx solid #ffffff;
				}
			}

			.user-info {
				margin-left: 20rpx;
				flex: 1;

				.name-area {
					display: flex;
					align-items: center;
					margin-bottom: 16rpx;

					.nickname {
						font-size: 34rpx;
						font-weight: bold;
						color: #333333;
						margin-right: 16rpx;
					}

					.service-indicator {
						display: flex;
						align-items: center;
						background-color: rgba(82, 196, 26, 0.1);
						border-radius: 20rpx;
						padding: 4rpx 12rpx;

						.indicator-dot {
							width: 10rpx;
							height: 10rpx;
							border-radius: 50%;
							background-color: #52c41a;
							margin-right: 6rpx;
						}

						.service-text {
							font-size: 20rpx;
							color: #52c41a;
						}
					}
				}

				.tags-area {
					display: flex;
					align-items: center;

					.tag-box {
						background: linear-gradient(90deg, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
						border-radius: 20rpx;
						padding: 6rpx 20rpx;
						display: inline-block;
						margin-right: 12rpx;

						.tag-text {
							font-size: 22rpx;
							color: #ffffff;
						}
					}

					.service-years {
						background-color: #fff7e6;
						border-radius: 20rpx;
						padding: 6rpx 16rpx;

						.years-text {
							font-size: 22rpx;
							color: #fa8c16;
							font-weight: 500;
						}
					}
				}
			}

			.status-box {
				display: flex;
				align-items: center;
				background-color: rgba(82, 196, 26, 0.1);
				border-radius: 30rpx;
				padding: 8rpx 20rpx;

				.status-dot {
					width: 12rpx;
					height: 12rpx;
					border-radius: 50%;
					background-color: #52c41a;
					margin-right: 8rpx;
				}

				.status-text {
					font-size: 26rpx;
					color: #52c41a;
					font-weight: 500;
				}
			}
		}

		.card-body {
			padding: 10rpx 0 30rpx;

			.info-divider {
				display: flex;
				align-items: center;
				margin-bottom: 30rpx;

				.divider-line {
					flex: 1;
					height: 1px;
					background-color: #f0f0f0;
				}

				.divider-text {
					padding: 0 20rpx;
					font-size: 24rpx;
					color: #999999;
				}
			}

			.info-grid {
				display: grid;
				grid-template-columns: 1fr 1fr;
				gap: 30rpx 20rpx;

				.info-grid-item {
					display: flex;
					flex-direction: column;
					align-items: center;
					padding: 20rpx;
					border-radius: 16rpx;
					background-color: #f9f9f9;

					.info-icon-container {
						display: flex;
						justify-content: center;
						align-items: center;
						width: 60rpx;
						height: 60rpx;
						border-radius: 30rpx;
						background-color: rgba(var(--ui-BG-Main), 0.1);
						margin-bottom: 12rpx;
					}

					.info-label {
						font-size: 24rpx;
						color: #999999;
						margin-bottom: 8rpx;
					}

					.info-value {
						font-size: 28rpx;
						color: #333333;
						font-weight: bold;
					}
				}
			}
		}

		.card-footer {
			display: flex;
			justify-content: space-between;
			gap: 20rpx;
			margin-top: 20rpx;

			.action-btn {
				display: flex;
				align-items: center;
				justify-content: center;
				flex: 1;
				height: 90rpx;
				border-radius: 45rpx;
				font-size: 28rpx;
				line-height: 90rpx;
				transition: all 0.3s ease;

				.btn-text {
					margin-left: 8rpx;
				}
			}

			.contact-btn {
				background: linear-gradient(90deg, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
				color: #ffffff;
				box-shadow: 0 8rpx 20rpx rgba(var(--ui-BG-Main), 0.3);

				&:active {
					transform: translateY(-2rpx);
					box-shadow: 0 12rpx 24rpx rgba(var(--ui-BG-Main), 0.4);
				}
			}

			.unbind-btn {
				background: #ffffff;
				color: #ff4d4f;
				border: 2rpx solid #ff4d4f;
				box-shadow: 0 4rpx 16rpx rgba(255, 77, 79, 0.15);

				&:active {
					transform: translateY(-2rpx);
					background: #fff2f0;
					box-shadow: 0 8rpx 20rpx rgba(255, 77, 79, 0.25);
				}
			}
		}
	}

	/* 无接单员状态 */
	.empty-state {
		background-color: #ffffff;
		border-radius: 20rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		padding: 60rpx 30rpx;
		margin-bottom: 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;

		.empty-image {
			width: 200rpx;
			height: 200rpx;
			margin-bottom: 30rpx;
		}

		.empty-text {
			font-size: 32rpx;
			color: #333333;
			font-weight: bold;
			margin-bottom: 15rpx;
		}

		.empty-desc {
			font-size: 28rpx;
			color: #999999;
			margin-bottom: 40rpx;
		}

		.bind-btn {
			width: 400rpx;
			height: 80rpx;
			border-radius: 40rpx;
			color: #ffffff;
			font-size: 30rpx;
			line-height: 80rpx;
		}
	}

	/* 信息说明区域 */
	.info-section {
		background-color: #ffffff;
		border-radius: 20rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		padding: 30rpx;

		.section-title {
			display: flex;
			align-items: center;
			margin-bottom: 30rpx;

			.title-icon {
				width: 8rpx;
				height: 30rpx;
				background-color: var(--ui-BG-Main);
				border-radius: 4rpx;
				margin-right: 20rpx;
			}

			.title-text {
				font-size: 32rpx;
				color: #333333;
				font-weight: bold;
			}
		}

		.info-content {
			.info-item {
				margin-bottom: 20rpx;

				&:last-child {
					margin-bottom: 0;
				}

				.info-icon {
					margin-right: 15rpx;
					display: flex;
					align-items: center;
				}

				.info-text {
					font-size: 28rpx;
					color: #666666;
					line-height: 40rpx;
				}
			}
		}
	}

	/* 解绑确认弹窗 */
	.unbind-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
		padding: 40rpx;

		.modal-content {
			background: #ffffff;
			border-radius: 24rpx;
			width: 100%;
			max-width: 600rpx;
			overflow: hidden;
			animation: modalSlideIn 0.3s ease-out;
		}

		.modal-header {
			padding: 40rpx 30rpx 20rpx;
			text-align: center;

			.warning-icon {
				margin-bottom: 20rpx;
			}

			.modal-title {
				font-size: 36rpx;
				font-weight: bold;
				color: #333333;
			}
		}

		.modal-body {
			padding: 0 30rpx 30rpx;

			.modal-desc {
				font-size: 28rpx;
				color: #666666;
				margin-bottom: 24rpx;
				display: block;
			}

			.privilege-list {
				background: #f8f9fa;
				border-radius: 16rpx;
				padding: 24rpx;
				margin-bottom: 24rpx;

				.privilege-item {
					display: flex;
					align-items: center;
					margin-bottom: 16rpx;

					&:last-child {
						margin-bottom: 0;
					}

					.privilege-dot {
						width: 8rpx;
						height: 8rpx;
						border-radius: 50%;
						background: #ff4d4f;
						margin-right: 16rpx;
						flex-shrink: 0;
					}

					.privilege-text {
						font-size: 26rpx;
						color: #666666;
						line-height: 1.4;
					}
				}
			}

			.modal-warning {
				font-size: 24rpx;
				color: #999999;
				text-align: center;
				display: block;
			}
		}

		.modal-footer {
			display: flex;
			border-top: 1rpx solid #f0f0f0;

			.modal-btn {
				flex: 1;
				height: 100rpx;
				line-height: 100rpx;
				text-align: center;
				font-size: 32rpx;
				border: none;
				background: none;
				transition: background-color 0.3s ease;

				&:active {
					background: #f5f5f5;
				}

				&::after {
					border: none;
					border-radius: 0;
				}
			}

			.cancel-btn {
				color: #666666;
				border-right: 1rpx solid #f0f0f0;
			}

			.confirm-btn {
				color: #ff4d4f;
				font-weight: bold;
			}
		}
	}

	@keyframes modalSlideIn {
		from {
			opacity: 0;
			transform: translateY(-50rpx) scale(0.9);
		}
		to {
			opacity: 1;
			transform: translateY(0) scale(1);
		}
	}
</style>
