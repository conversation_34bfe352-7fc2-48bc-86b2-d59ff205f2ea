<template>
	<s-layout title="接单员邀请" navbar="normal" :onShareAppMessage="state.shareInfo">
		<view class="page-container">
			<!-- 头部信息卡片 -->
			<view class="header-card">
				<view class="header-content">
					<view class="title-section">
						<text class="main-title">接单员</text>
						<text class="subtitle">享受专属收益，开启赚钱之旅</text>
					</view>
					<view class="avatar-section">
						<view class="avatar-bg">
							<uni-icons type="person-filled" size="32" color="#ffffff"></uni-icons>
						</view>
					</view>
				</view>
			</view>

			<!-- 邀请码卡片 -->
			<view class="code-card">
				<view class="code-header">
					<text class="code-title">我的邀请码</text>
					<text class="code-desc">分享给好友，共享收益</text>
				</view>
				<view class="code-display">
					<text class="invitation-code">{{ userInfo.shareCode }}</text>
					<view class="code-copy" @tap="copyCode">
						<uni-icons type="redo-filled" size="20" color="#ffffff"></uni-icons>
					</view>
				</view>
			</view>

			<!-- 特色功能卡片 -->
			<view class="features-card">
				<view class="features-header">
					<text class="features-title">平台优势</text>
				</view>
				<view class="features-grid">
					<view class="feature-item">
						<view class="feature-icon">
							<uni-icons type="shop-filled" size="24" color="#ff6000"></uni-icons>
						</view>
						<text class="feature-text">快速接单</text>
						<text class="feature-desc">海量订单等你来</text>
					</view>
					<view class="feature-item">
						<view class="feature-icon">
							<uni-icons type="wallet-filled" size="24" color="#2aae67"></uni-icons>
						</view>
						<text class="feature-text">灵活收益</text>
						<text class="feature-desc">多劳多得自由赚</text>
					</view>
					<view class="feature-item">
						<view class="feature-icon">
							<uni-icons type="gift-filled" size="24" color="#e9b461"></uni-icons>
						</view>
						<text class="feature-text">邀请奖励</text>
						<text class="feature-desc">推荐好友获佣金</text>
					</view>
				</view>
			</view>

			<!-- 操作按钮 -->
			<view class="action-section">
				<button open-type="share" class="ss-reset-button share-button">
					<uni-icons type="paperplane" size="20" color="#ffffff"></uni-icons>
					<text>立即邀请好友</text>
				</button>

				<view class="record-button" @tap="handleViewBindRecords">
					<uni-icons type="list" size="18" color="#666666"></uni-icons>
					<text>查看绑定记录</text>
				</view>
			</view>
		</view>
	</s-layout>
</template>

<script setup>
	import sheep from '@/sheep';
	import { computed, reactive, getCurrentInstance } from 'vue';
	import { onLoad, onShow } from '@dcloudio/uni-app';

	// 获取当前实例
	const { proxy } = getCurrentInstance();

	const state = reactive({
		shareInfo: {},
	});

	// 用户信息
	const userInfo = computed(() => sheep.$store('user').userInfo);

	const handleInvite = () => {
		console.log('Invite button clicked!');
		uni.showToast({
			title: '准备分享您的邀请！',
			icon: 'none',
		});
	};

	// 复制邀请码
	const copyCode = () => {
		uni.setClipboardData({
			data: userInfo.value.shareCode,
			success: () => {
				sheep.$helper.toast('邀请码已复制');
			},
			fail: () => {
				sheep.$helper.toast('复制失败');
			},
		});
	};

	// 分享参数
	const shareParam = () => {
		state.shareInfo.title = '诚邀您绑定我为专属接单员，尊享独家优惠礼遇';
		state.shareInfo.path = `/pages/user/bind/share-user?u=${userInfo.value.id}&t=1&n=${userInfo.value.nickname}&c=${userInfo.value.shareCode}`;
		proxy.share.title = '诚邀您绑定我为专属接单员，尊享独家优惠礼遇';
		proxy.share.path = `/pages/user/bind/share-user?u=${userInfo.value.id}&t=1&n=${userInfo.value.nickname}&c=${userInfo.value.shareCode}`;
	};

	// 查看绑定记录
	const handleViewBindRecords = () => {
		sheep.$router.go('/pages/user/bind/record');
	};

	onLoad(async () => {
		const data = await sheep.$api.user.profile();
		if (data.isOrderTaker == 0) {
			uni.showModal({
				title: '提示',
				content: '非接单员身份，无法查看绑定记录。如需帮助请联系管理员。',
				showCancel: false,
				confirmText: '返回',
				success: (res) => {
					if (res.confirm) {
						sheep.$router.go('/pages/index/user');
					}
				},
			});
			return;
		} else {
			shareParam();
		}
	});
</script>

<style lang="scss" scoped>
	.page-container {
		background-color: #f6f6f6;
		min-height: 100vh;
		padding: 30rpx;
		box-sizing: border-box;
	}

	// 头部信息卡片
	.header-card {
		background: linear-gradient(135deg, $orange 0%, #ff8533 100%);
		border-radius: 20rpx;
		padding: 40rpx 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 8rpx 32rpx rgba(255, 96, 0, 0.25);

		.header-content {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}

		.title-section {
			flex: 1;

			.main-title {
				display: block;
				font-size: 44rpx;
				font-weight: bold;
				color: #ffffff;
				margin-bottom: 12rpx;
				line-height: 1.2;
			}

			.subtitle {
				display: block;
				font-size: 28rpx;
				color: rgba(255, 255, 255, 0.9);
				line-height: 1.4;
			}
		}

		.avatar-section {
			.avatar-bg {
				width: 80rpx;
				height: 80rpx;
				background: rgba(255, 255, 255, 0.2);
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				backdrop-filter: blur(10rpx);
			}
		}
	}

	// 邀请码卡片
	.code-card {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

		.code-header {
			margin-bottom: 30rpx;

			.code-title {
				display: block;
				font-size: 32rpx;
				font-weight: bold;
				color: #333333;
				margin-bottom: 8rpx;
			}

			.code-desc {
				display: block;
				font-size: 26rpx;
				color: #666666;
			}
		}

		.code-display {
			background: linear-gradient(135deg, #fff8f0 0%, #ffede0 100%);
			border-radius: 16rpx;
			padding: 30rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			border: 2rpx solid #ffe0cc;

			.invitation-code {
				font-size: 48rpx;
				font-weight: bold;
				color: $orange;
				letter-spacing: 4rpx;
				flex: 1;
			}

			.code-copy {
				width: 60rpx;
				height: 60rpx;
				background: $orange;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				transition: all 0.3s ease;

				&:active {
					transform: scale(0.95);
					background: #e55500;
				}
			}
		}
	}

	// 特色功能卡片
	.features-card {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

		.features-header {
			margin-bottom: 30rpx;

			.features-title {
				display: block;
				font-size: 32rpx;
				font-weight: bold;
				color: #333333;
			}
		}

		.features-grid {
			display: flex;
			justify-content: space-between;
			gap: 20rpx;
		}

		.feature-item {
			flex: 1;
			text-align: center;
			padding: 20rpx 10rpx;
			border-radius: 16rpx;
			background: #f8f9fa;
			transition: all 0.3s ease;

			&:active {
				transform: translateY(-4rpx);
				box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
			}

			.feature-icon {
				width: 60rpx;
				height: 60rpx;
				border-radius: 50%;
				background: rgba(255, 255, 255, 0.8);
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 0 auto 16rpx;
				box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
			}

			.feature-text {
				display: block;
				font-size: 28rpx;
				font-weight: bold;
				color: #333333;
				margin-bottom: 8rpx;
			}

			.feature-desc {
				display: block;
				font-size: 22rpx;
				color: #666666;
				line-height: 1.4;
			}
		}
	}

	// 操作按钮区域
	.action-section {
		.share-button {
			background: linear-gradient(135deg, $orange 0%, #ff8533 100%);
			border-radius: 50rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 12rpx;
			box-shadow: 0 8rpx 24rpx rgba(255, 96, 0, 0.3);
			transition: all 0.3s ease;
			margin-bottom: 20rpx;

			text {
				font-size: 32rpx;
				font-weight: bold;
				color: #ffffff;
			}

			&:active {
				transform: translateY(-2rpx);
				box-shadow: 0 12rpx 32rpx rgba(255, 96, 0, 0.4);
			}
		}

		.record-button {
			background: #ffffff;
			border-radius: 50rpx;
			padding: 20rpx 40rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 12rpx;
			box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
			transition: all 0.3s ease;
			border: 2rpx solid #f0f0f0;

			text {
				font-size: 28rpx;
				color: #666666;
			}

			&:active {
				transform: translateY(-2rpx);
				box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.12);
				border-color: #e0e0e0;
			}
		}
	}
</style>
