<template>
  <s-layout title="我的礼品">
    <view class="container" v-if="state.shareList.length">
      <uni-list v-for="(item, index) in state.shareList" :key="index" @tap="onDetail(item)">
        <uni-list-item :title="'订单号：'+item.orderId"  :note="'支付时间：'+item.paymentTime" showArrow :thumb="item.orderItemList[0]?.pic" thumb-size="lg" rightText="已领取" />
      </uni-list>
    </view>
    <view class="container" v-else>
      <s-empty text="暂无礼品记录" icon="https://bjnkdpxshop.oss-cn-beijing.aliyuncs.com/2023/09/130677889cd8d844c497db5f08b8386fcehistory.png"></s-empty>
    </view>
  </s-layout>
</template>

<script setup>
import { reactive } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import sheep from '@/sheep';
import sEmpty from '@/sheep/components/s-empty/s-empty.vue';

const state = reactive({
  shareList: []
})

function onDetail({ orderId }) {
  sheep.$router.go('/pages/order/detail', {
    id: orderId,
    share: 1
  });
}

onLoad(() => {
  getShareRecord();
})

const getShareRecord = async () => {
  const res = await sheep.$api.user.getShareRecord();
  if (res.code == 200)
    state.shareList = res.data;
};
</script>

<style lang="scss" scoped>
.container {
  background: #fff;
}
</style>
