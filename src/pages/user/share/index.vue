<template>
  <s-layout class="share-layout" title="我的分享" :onShareAppMessage="state.shareInfo">
    <view class="container">
      <image mode="widthFix" class="bg-image" src="https://bjnkdpxshop.oss-cn-beijing.aliyuncs.com/2023/09/132aed9db333314a698931c41f6d13e0a9qrcode2.jpg"></image>
      <!-- <view v-for="(item, index) in state.shareList" :key="index" @tap="onDetail(item)">
        <uni-list-item :title="item.receiverName+'('+item.receiverPhone+')'" :note="item.receiverProvince+item.receiverCity+item.receiverDistrict+item.receiverDetailAddress" showArrow :thumb="item.orderItemList[0]?.pic" thumb-size="lg" rightText="已领取" />
      </view> -->
      <image class="bg-code" :src="state.url"></image>
    </view>
    <view class="shars-btns">
      <view class="btns-box">
        <button open-type="share" class="ss-reset-button forward-friends">转发朋友</button>
        <button class="ss-reset-button forward-friends" @tap="onShareRecord">分享记录</button>
      </view>
      <!-- <view class="share-record" @tap="onShareRecord">分享记录</view> -->
    </view>
    <canvas-poster ref="SharePosterRef" :show="state.showPosterModal" :shareInfo="state.shareInfo" @close="state.showPosterModal = false" />
  </s-layout>
</template>

<script setup>
import { reactive, computed, getCurrentInstance, unref, ref } from 'vue';
import canvasPoster from '@/sheep/components/s-share-modal/canvas-poster/index.vue';
import { onLoad, onShow } from '@dcloudio/uni-app';
import sheep from '@/sheep';
import $platform from '@/sheep/platform';
import { showAuthModal } from '@/sheep/hooks/useModal';

const { proxy } = getCurrentInstance();
const userInfo = computed(() => sheep.$store('user').userInfo);

const state = reactive({
  url: "",
  shareInfo: {},
  userInfo: "",
  showPosterModal: false,
  bindUid: '',
  bindTid: ''
})
state.userInfo = userInfo;
function shareParam() {
  state.shareInfo.title = "无忧送礼，您下单，Ta填地址";
  state.shareInfo.path = `/pages/user/share/index?u=${state.userInfo.id}&t=1`;
  proxy.share.title = "无忧送礼，您下单，Ta填地址";
  proxy.share.path = `/pages/user/share/index?u=${state.userInfo.id}&t=1`;
}

function onShareRecord() {
  sheep.$router.go('/pages/user/share/record');
}

function getUrlCode(name, link) {
  return (
    decodeURIComponent(
      (new RegExp("[?|&]" + name + "=" + "([^&;]+?)(&|#|;|$)").exec(
        '?' + link
      ) || [, ""])[1].replace(/\+/g, "%20")
    ) || null
  );
}

const getCode = async () => {
  const res = await sheep.$api.user.getCode();
  console.log(res)
  if (res.code == 200)
    state.url = res.url;
};


async function bindUser(u) {
  if (sheep.$store('user').isLogin) {
    const shareRes = await sheep.$api.user.bindShareUser(u);
    if (shareRes.code == 200) {
      sheep.$store('user').setBindShareUser({ u: '', t: '' });
      uni.showModal({
        title: '提示',
        content: '您已绑定成功',
        showCancel: false,
        confirmText: '返回首页',
        success: ({ confirm }) => {
          if (confirm) {
            sheep.$router.go('/pages/index/index')
          }
        }
      });
    }
  }
}

onLoad((options) => {
  // #ifdef MP
  // 小程序识别二维码
  console.log(options)
  if (options.scene) {
    const sceneParams = decodeURIComponent(options.scene);
    const u = getUrlCode('u', sceneParams);
    const t = getUrlCode('t', sceneParams);
    state.bindUid = u;
    state.bindTid = t;
    sheep.$store('user').setBindShareUser({
      u,
      t
    });
  } else if (options.u && options.t) {
    sheep.$store('user').setBindShareUser({
      u: options.u,
      t: options.t
    });
    state.bindUid = options.u;
    state.bindTid = options.t;
  }
  // #endif
  getCode();
  shareParam();
})

onShow(() => {
  if (!sheep.$store('user').isLogin && state.bindUid) {
    if ($platform.name === 'WechatMiniProgram') {
      showAuthModal('wechatMiniLogin')
    } else {
      showAuthModal('smsLogin')
    }
  }
  if (state.bindTid && state.bindUid) {
    bindUser(state.bindUid)
  }
})


</script>

<style lang="scss" scoped>
.container {
  background: #fff;
  position: relative;
  height: 100%;

  .bg-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
  }

  .bg-code {
    position: absolute;
    top: 504rpx;
    left: 49%;
    z-index: 2;
    width: 318rpx;
    height: 318rpx;
    transform: translateX(-50%);
  }
}

.shars-btns {
  position: fixed;
  bottom: 100rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  justify-content: center;

  .btns-box {
    display: flex;
  }

  .forward-friends {
    width: 220rpx;
    border-radius: 10rpx;
    height: 80rpx;
    background: #fff;
    font-size: 28rpx;
    box-shadow: 0 0 10rpx 3rpx rgba($color: #fff, $alpha: 0.4);

    &:first-child {
      margin-right: 30px;
    }
  }

  .share-record {
    width: 100%;
    justify-content: center;
    font-size: 32rpx;
    color: #fff;
    text-align: center;
    margin-top: 30rpx;
    text-decoration: underline;
    font-weight: bold;
  }
}
</style>
