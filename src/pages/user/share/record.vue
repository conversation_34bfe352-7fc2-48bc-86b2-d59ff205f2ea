<template>
  <s-layout title="分享记录">
    <view class="container" v-if="state.list.length">
      <uni-list>
        <uni-list-item v-for="(item, index) in state.list" :key="index" :title="'绑定用户-'+item.nickname" />
      </uni-list>
    </view>
    <view class="container" v-else>
      <s-empty text="暂无记录" icon="https://bjnkdpxshop.oss-cn-beijing.aliyuncs.com/2023/09/130677889cd8d844c497db5f08b8386fcehistory.png"></s-empty>
    </view>
  </s-layout>
</template>

<script setup>
import { reactive } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import sheep from '@/sheep';
import sEmpty from '@/sheep/components/s-empty/s-empty.vue';

const state = reactive({
  list: []
})

onLoad(() => {
  getMyQrCodeRecord();
})

const getMyQrCodeRecord = async () => {
  const res = await sheep.$api.user.getMyQrCodeRecord();
  if (res.code == 200)
    state.list = res.data;
};
</script>

<style lang="scss" scoped>
.container {
  background: #fff;
}
</style>
