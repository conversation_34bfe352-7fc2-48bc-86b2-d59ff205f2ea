<template>
	<view>
		<s-layout title="首页" navbar="custom" tabbar="/pages/index/index" :navbarStyle="template.style?.navbar" :onShareAppMessage="state.shareInfo">
			<view class="ad-content" v-if="adList.length" :style="{ marginTop: getAdMarginTop }">
				<image :src="it.pic" v-for="(it, index) in adList" :key="index" mode="widthFix" @tap="handleBannerClick(it)"></image>
			</view>
			<!--轮播图 -->
			<view class="banner-content" :style="{ marginTop: getMarginTop }">
				<swiper class="swiper-content" :indicator-dots="true" :autoplay="true" :circular="true">
					<swiper-item v-for="it in bannerList" :key="it.id" @tap="handleBannerClick(it)">
						<image :src="it.pic" class="img" />
					</swiper-item>
				</swiper>
			</view>
			<!-- 分类 -->
			<view class="category-wrapper">
				<view class="category-content">
					<view class="category-item" v-for="(it, idx) in categoryList" :key="it.id" @tap="sheep.$router.go('/pages/goods/list', { categoryId: it.id })">
						<view class="icon-container">
							<image :src="it.icon" class="ct-icon" mode="aspectFit" />
							<view class="shine-effect"></view>
						</view>
						<view class="ct-text">{{ it.name }}</view>
					</view>
				</view>
			</view>
			<!-- 广告模块 -->
			<!-- <s-popup-image /> -->
			<view class="icon-text">
				<view class="recommend-tag">
					<view class="tag-decoration"></view>
				</view>
				<view class="text-info">
					<view class="left"></view>
					<view class="center">
						<view class="title-icon-left"></view>
						<text class="title-text">推荐商品</text>
						<view class="title-icon-right"></view>
					</view>
					<view class="right"></view>
				</view>
			</view>
			<view class="goods-block">
				<s-goods-card :data="goodsCard.data" :styles="goodsCard.style" />
			</view>
			 <s-ai-btn @open-ai-assistant="openAiAssistant" /> 
		</s-layout>
	</view>
</template>

<script setup>
	import { computed, ref, reactive, getCurrentInstance } from 'vue';
	import { onLoad, onShow, onPageScroll, onPullDownRefresh, onReachBottom, onShareTimeline } from '@dcloudio/uni-app';
	import sheep from '@/sheep';
	import $share from '@/sheep/platform/share';
	//#ifdef H5
	import weixin from '@/sheep/libs/sdk-h5-weixin';
	//#endif
	import $platform from '@/sheep/platform';
	import { showAuthModal } from '@/sheep/hooks/useModal';
	// 获取当前实例
	const { proxy } = getCurrentInstance();
	// 分类列表
	const categoryList = ref([]);
	// 轮播图列表
	const bannerList = ref([]);
	// 广告列表
	const adList = ref([]);
	// 状态
	const state = reactive({
		shareInfo: {},
		share: {},
	});
	// 商品卡片
	const goodsCard = {
		data: {
			mode: 2,
			goodsFields: {
				title: {
					show: 1,
				},
				subtitle: {
					show: 1,
				},
				price: {
					show: 1,
				},
				original_price: {
					show: 1,
				},
				sales: {
					show: 1,
				},
				stock: {
					show: 0,
				},
			},
			buyNowStyle: {
				// "mode": 2,
				// "text": "立即购买",
				// "color1": "#E9B461",
				// "color2": "#EECC89",
				// "src": "\/storage\/decorate\/20221115\/4782356b4587dc4f4a218f2540a0bafc.png",
				// "right": '20rpx',
				// "bottom":"18rpx"
			},
			tagStyle: {
				show: 0,
				src: '',
			},
			params: {
				orderField: 'sort',
				orderSort: 'asc',
			},
			borderRadiusTop: 6,
			borderRadiusBottom: 6,
			space: 8,
		},
		style: {
			background: {
				type: 'color',
				bgImage: '',
				bgColor: '',
			},
			marginLeft: 8,
			marginRight: 8,
			marginTop: 0,
			marginBottom: 10,
			borderRadiusTop: 0,
			borderRadiusBottom: 0,
			padding: 0,
		},
	};

	// 模板
	const template = computed(() => sheep.$store('app').template.home);

	// 分享信息
	function shareInformation() {
		state.shareInfo.title = '享受专属接单员优惠';
		proxy.share.title = '享受专属接单员优惠';
		proxy.share.imageUrl = adList.length ? adList[0].pic : 'https://bjnkdpxshop.oss-cn-beijing.aliyuncs.com/2023/09/06349125349c854ce2bcedd1cb439a4b57广告图.jpg';
	}

	// 轮播图距离顶部距离
	const getMarginTop = computed(() => {
		const statusBarHeight = sheep.$platform.device.statusBarHeight;
		if (adList.value.length) {
			return `20rpx`;
		} else {
			if (statusBarHeight <= 30) return `${statusBarHeight + 120}rpx`;
			else return `${statusBarHeight + 150}rpx`;
		}
	});

	// 广告模块距离顶部距离
	const getAdMarginTop = computed(() => {
		const statusBarHeight = sheep.$platform.device.statusBarHeight;
		if (statusBarHeight <= 30) return `${statusBarHeight + 120}rpx`;
		else return `${statusBarHeight + 150}rpx`;
	});

	const openAiAssistant = () => {
		console.log('openAiAssistant');
		sheep.$router.go('/pages/ai/index');
	};

	// onShareAppMessage((res) => {
	//   let shareData = {
	//     title: '邀请好友领取海量现金券!',
	//     desc: '我正在使用xxxApp，赶紧跟我一起来体验！',
	//     link: "https://mall.ichengle.top/uni/",
	//     imgUrl: 'https://bjetxgzv.cdn.bspapp.com/VKCEYUGU-uni-app-doc/d8590190-4f28-11eb-b680-7980c8a877b8.png'
	//   }
	//   return {
	//     ...shareData
	//   }
	// });

	//#ifdef H5
	// 微信h5分享；
	function setOpenShare() {
		let currentUrl = location.href; //获取当前页面链接

		let shareData = {
			title: '邀请好友领取海量现金券!',
			desc: '我正在使用xxxApp，赶紧跟我一起来体验！',
			link: 'https://mall.ichengle.top/uni/',
			imgUrl: 'https://bjetxgzv.cdn.bspapp.com/VKCEYUGU-uni-app-doc/d8590190-4f28-11eb-b680-7980c8a877b8.png',
		};
		console.log('更新分享', shareData);
		weixin.updateShareInfo(shareData);
	}
	//#endif

	// 获取商品分类
	function getCategoryList() {
		sheep.$api.category.list().then((res) => {
			categoryList.value = res;
		});
	}

	// 获取轮播图
	function getBannerList() {
		console.log(sheep.$api);
		sheep.$api.banner
			.list({
				pageNum: 1,
				pageSize: 33,
				isOpen: 1,
				type: 0,
			})
			.then((res) => {
				console.log('res', res);
				bannerList.value = res.rows;
			});
	}

	function getAdList() {
		console.log(sheep.$api);
		sheep.$api.banner
			.list({
				pageNum: 1,
				pageSize: 33,
				isOpen: 1,
				type: 1,
			})
			.then((res) => {
				console.log('res', res);
				adList.value = res.rows;
			});
	}

	/**
	 * 处理轮播图点击事件
	 * @param {Object} it 轮播图数据
	 */
	function handleBannerClick(it) {
		if (it.links) {
			const page = it.links.includes('http') ? false : it.links;
			if (page) {
				sheep.$router.go(page);
			} else {
				uni.navigateTo({
					url: `/pages/public/webview?url=${it.links}`,
				});
			}
		}
	}

	function requestAll(url, data) {
		getAdList();
		getBannerList();
		getCategoryList();
	}

	// 隐藏原生tabBar
	uni.hideTabBar();

	// 下拉刷新
	onPullDownRefresh(() => {
		sheep.$store('app').init();
		requestAll();
		setTimeout(function () {
			uni.stopPullDownRefresh();
		}, 800);
	});

	// 上拉加载更多
	onReachBottom(() => {
		// 该事件必须申明,子组件才会触发该事件
	});

	onPageScroll(() => {});

	onLoad((options) => {
		// #ifdef MP
		// 小程序识别二维码
		if (options.scene) {
			const sceneParams = decodeURIComponent(options.scene).split('=');
			options[sceneParams[0]] = sceneParams[1];
		}
		// #endif

		// 预览模板
		if (options.templateId) {
			sheep.$store('app').init(options.templateId);
		}

		// 解析分享信息
		if (options.spm) {
			$share.decryptSpm(options.spm);
		}

		// 进入指定页面(完整页面路径)
		if (options.page) {
			sheep.$router.go(decodeURIComponent(options.page));
		}
		requestAll();
		shareInformation();
		//#ifdef H5
		setOpenShare();
		//#endif

		console.log('template', sheep.$platform.device.statusBarHeight);
	});

	onShow(() => {
		// if (!sheep.$store('user').isLogin) {
		//     if ($platform.name === 'WechatMiniProgram') {
		//       showAuthModal('wechatMiniLogin')
		//     } else {
		//       showAuthModal('smsLogin')
		//     }
		//   }
	});
</script>

<style lang="scss" scoped>
	.ad-content {
		margin: 0 20rpx;
		margin-bottom: 0;
		border-radius: 10rpx;

		image {
			width: 100%;
			border-radius: 10rpx;
		}
	}
	.banner-content {
		margin: 0 20rpx;
		margin-bottom: 0;
		height: 332rpx;
		border-radius: 10rpx;

		.swiper-content {
			width: 100%;
			height: 100%;
			border-radius: 10rpx;

			image {
				width: 100%;
				height: 100%;
				border-radius: 10rpx;
			}
		}
	}
	.goods-block {
		/* #ifdef MP-WEIXIN */
		margin: 140rpx 20rpx;
		margin-top: 30rpx;
		/* #endif */

		/* #ifdef H5 */
		margin: 90rpx 20rpx;
		/* #endif */
	}

	.icon-text {
		display: flex;
		justify-content: center;
		margin: 60rpx 0 60rpx;
		position: relative;

		.recommend-tag {
			position: absolute;
			top: -20rpx;
			left: 50%;
			transform: translateX(-50%);
			background: linear-gradient(135deg, #e9b461, #f3c988);
			width: 70rpx;
			height: 10rpx;
			border-radius: 10rpx;
			box-shadow: 0 4rpx 8rpx rgba(233, 180, 97, 0.3);
			z-index: 10;

			.tag-decoration {
				position: relative;
				width: 100%;
				height: 100%;

				&::before,
				&::after {
					content: '';
					position: absolute;
					width: 8rpx;
					height: 8rpx;
					background-color: rgba(233, 180, 97, 0.8);
					border-radius: 50%;
					top: 50%;
					transform: translateY(-50%);
				}

				&::before {
					left: -12rpx;
				}

				&::after {
					right: -12rpx;
				}
			}

			&::after {
				content: '';
				position: absolute;
				bottom: -6rpx;
				left: 50%;
				transform: translateX(-50%) rotate(45deg);
				width: 12rpx;
				height: 12rpx;
				background: linear-gradient(135deg, #e9b461, #f3c988);
			}
		}

		.text-info {
			display: flex;
			align-items: center;
			width: 600rpx;
			position: relative;
			z-index: 2;

			&::after {
				content: '';
				position: absolute;
				bottom: -25rpx;
				left: 50%;
				transform: translateX(-50%);
				width: 240rpx;
				height: 6rpx;
				background: linear-gradient(to right, transparent, rgba(233, 180, 97, 0.15), transparent);
				border-radius: 3rpx;
			}

			.left {
				flex: 1;
				height: 2rpx;
				background: linear-gradient(to right, rgba(233, 180, 97, 0), rgba(233, 180, 97, 0.8));
				position: relative;
				overflow: hidden;

				&::after {
					content: '';
					position: absolute;
					top: 0;
					right: 0;
					width: 40rpx;
					height: 100%;
					background: linear-gradient(to right, rgba(233, 180, 97, 0), rgba(233, 180, 97, 1));
					animation: shine 2s ease-in-out infinite;
				}
			}

			.center {
				position: relative;
				padding: 0 20rpx;
				display: flex;
				flex-direction: column;
				align-items: center;

				.title-icon-left,
				.title-icon-right {
					position: absolute;
					top: 50%;
					transform: translateY(-60%);
					width: 40rpx;
					height: 40rpx;
					z-index: 2;

					&::before,
					&::after {
						content: '';
						position: absolute;
						background-color: rgba(233, 180, 97, 0.8);
					}
				}

				.title-icon-left {
					left: -20rpx;

					&::before {
						left: 10rpx;
						top: 10rpx;
						width: 10rpx;
						height: 10rpx;
						border-radius: 50%;
						box-shadow: 0 0 8rpx rgba(233, 180, 97, 0.8);
					}

					&::after {
						left: 25rpx;
						top: 25rpx;
						width: 6rpx;
						height: 6rpx;
						border-radius: 50%;
						box-shadow: 0 0 6rpx rgba(233, 180, 97, 0.8);
					}
				}

				.title-icon-right {
					right: -20rpx;

					&::before {
						right: 10rpx;
						top: 10rpx;
						width: 10rpx;
						height: 10rpx;
						border-radius: 50%;
						box-shadow: 0 0 8rpx rgba(233, 180, 97, 0.8);
					}

					&::after {
						right: 25rpx;
						top: 25rpx;
						width: 6rpx;
						height: 6rpx;
						border-radius: 50%;
						box-shadow: 0 0 6rpx rgba(233, 180, 97, 0.8);
					}
				}

				.title-text {
					font-size: 36rpx;
					font-weight: 600;
					color: #333333;
					line-height: 1.2;
					position: relative;
					background: linear-gradient(to bottom, #333333, #666666);
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
					letter-spacing: 2rpx;
					text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);

					&::before,
					&::after {
						content: '✦';
						position: absolute;
						top: 50%;
						transform: translateY(-50%);
						font-size: 24rpx;
						color: rgba(233, 180, 97, 0.8);
					}

					&::before {
						left: -28rpx;
					}

					&::after {
						right: -28rpx;
					}
				}
			}

			.right {
				flex: 1;
				height: 2rpx;
				background: linear-gradient(to left, rgba(233, 180, 97, 0), rgba(233, 180, 97, 0.8));
				position: relative;
				overflow: hidden;

				&::after {
					content: '';
					position: absolute;
					top: 0;
					left: 0;
					width: 40rpx;
					height: 100%;
					background: linear-gradient(to left, rgba(233, 180, 97, 0), rgba(233, 180, 97, 1));
					animation: shine-reverse 2s ease-in-out infinite;
					animation-delay: 1s;
				}
			}
		}
	}

	@keyframes shine {
		0%,
		100% {
			transform: translateX(-100%);
			opacity: 0;
		}
		50% {
			transform: translateX(100%);
			opacity: 0.8;
		}
	}

	@keyframes shine-reverse {
		0%,
		100% {
			transform: translateX(100%);
			opacity: 0;
		}
		50% {
			transform: translateX(-100%);
			opacity: 0.8;
		}
	}

	.category-wrapper {
		margin: 30rpx 20rpx 40rpx;
		border-radius: 18rpx;
		box-shadow: 0 6rpx 24rpx rgba(233, 180, 97, 0.08);
		padding: 25rpx 0;
		position: relative;
		overflow: hidden;
	}

	.category-content {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 15rpx 10rpx;
		padding: 0 10rpx;

		.category-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 15rpx 0;
			position: relative;
			transition: all 0.25s;

			&:active {
				transform: translateY(-5rpx) scale(0.96);
			}

			.icon-container {
				position: relative;
				width: 100rpx;
				height: 100rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-bottom: 15rpx;

				.shine-effect {
					position: absolute;
					top: -10rpx;
					left: -10rpx;
					width: 20rpx;
					height: 20rpx;
					background: rgba(233, 180, 97, 0.8);
					border-radius: 50%;
					filter: blur(5rpx);
					opacity: 0;
					z-index: 3;
					animation: float 4s infinite;
					animation-delay: calc(var(--i) * 0.5s);
				}

				&::before {
					content: '';
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					bottom: 0;
					background: linear-gradient(135deg, rgba(233, 180, 97, 0.2), rgba(238, 204, 137, 0.01));
					border-radius: 18rpx;
					transform: rotate(45deg);
					z-index: 1;
					transition: all 0.3s;
					box-shadow: 0 4rpx 12rpx -2rpx rgba(233, 180, 97, 0.2);
				}

				&::after {
					content: '';
					position: absolute;
					top: -5%;
					left: -5%;
					right: -5%;
					bottom: -5%;
					border-radius: 20rpx;
					background: radial-gradient(circle at 50% 50%, rgba(233, 180, 97, 0.15), transparent 60%);
					transform: rotate(45deg);
					z-index: 0;
					opacity: 0;
					transition: opacity 0.3s;
				}
			}

			.ct-icon {
				width: 80rpx;
				height: 80rpx;
				position: relative;
				z-index: 2;
				border-radius: 50%;
				transition: all 0.3s;
				filter: drop-shadow(0 4rpx 3rpx rgba(0, 0, 0, 0.1));
			}

			.ct-text {
				font-size: 24rpx;
				color: #333;
				font-weight: 500;
				width: 100%;
				text-align: center;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				transition: all 0.3s;
				line-height: 1.3;
			}

			&:active {
				.icon-container::before {
					background: linear-gradient(135deg, rgba(233, 180, 97, 0.25), rgba(238, 204, 137, 0.1));
					box-shadow: 0 8rpx 15rpx -2rpx rgba(233, 180, 97, 0.25);
				}

				.icon-container::after {
					opacity: 1;
				}

				.ct-icon {
					transform: scale(1.08);
					filter: drop-shadow(0 6rpx 5rpx rgba(0, 0, 0, 0.15));
				}

				.ct-text {
					color: #e9b461;
					font-weight: 600;
					transform: scale(1.05);
				}
			}
		}
	}

	@keyframes float {
		0%,
		100% {
			opacity: 0;
		}
		50% {
			opacity: 0.8;
			transform: translate(70rpx, 70rpx) scale(1.5);
		}
	}
</style>
