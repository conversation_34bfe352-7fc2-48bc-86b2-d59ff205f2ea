<template>
	<view style="padding: 100rpx 30rpx">
		<view style="display: flex; flex-wrap: wrap; gap: 10rpx; align-items: center; justify-content: center">
			<view @tap="sheep.$helper.copyText(item+'')" v-for="item in iconList" :key="item" style=" width: 80rpx; height: 80rpx; background-color: #f0f0f0; font-size: 30rpx; text-align: center; line-height: 60rpx; display: flex; flex-direction: column; align-items: center; justify-content: center; flex-direction: column; border-radius: 10rpx; gap: 10rpx; cursor: pointer">
				<view :class="`${item}`" style="font-size: 30rpx; line-height: 30rpx"></view>
				<view style="font-size: 12rpx; line-height: 12rpx;">{{ item }}</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import sheep from '@/sheep';

	const iconList = [
		'cicon-Aa',
		'cicon-accounts',
		'cicon-accounts-o',
		'cicon-add',
		'cicon-add-round',
		'cicon-add-round-o',
		'cicon-alarm',
		'cicon-album',
		'cicon-alipay',
		'cicon-android',
		'cicon-angle',
		'cicon-apple',
		'cicon-apps',
		'cicon-archive',
		'cicon-archive-o',
		'cicon-arrow',
		'cicon-at-line',
		'cicon-avatar',
		'cicon-avatar-o',
		'cicon-avatars',
		'cicon-avatars-o',
		'cicon-back',
		'cicon-backspace',
		'cicon-backup',
		'cicon-backup-restore',
		'cicon-barcode',
		'cicon-book',
		'cicon-bookmark',
		'cicon-bookmark-o',
		'cicon-bookmarks',
		'cicon-box',
		'cicon-box-block',
		'cicon-box-right',
		'cicon-brand',
		'cicon-brand-o',
		'cicon-building',
		'cicon-building-o',
		'cicon-camera',
		'cicon-camera-add',
		'cicon-camera-add-o',
		'cicon-camera-lens',
		'cicon-camera-lens-o',
		'cicon-camera-o',
		'cicon-camera-rotate',
		'cicon-card',
		'cicon-cardboard',
		'cicon-cardboard-o',
		'cicon-cardboard-off-o',
		'cicon-cart',
		'cicon-cart-o',
		'cicon-chat',
		'cicon-chat-bubble',
		'cicon-chat-bubble-o',
		'cicon-chat-list',
		'cicon-chat-list-o',
		'cicon-chat-o',
		'cicon-chat-smile',
		'cicon-chat-smile-o',
		'cicon-chat-smiles',
		'cicon-chat-smiles-o',
		'cicon-check',
		'cicon-checkbox',
		'cicon-checkbox-o',
		'cicon-check-round',
		'cicon-check-round-o',
		'cicon-choiceness',
		'cicon-choiceness-o',
		'cicon-chrome',
		'cicon-circle',
		'cicon-circle-o',
		'cicon-close',
		'cicon-close-round',
		'cicon-close-round-o',
		'cicon-clothes',
		'cicon-clothes-o',
		'cicon-cloud',
		'cicon-cloud-done',
		'cicon-cloud-download',
		'cicon-cloud-o',
		'cicon-cloud-off',
		'cicon-cloud-upload',
		'cicon-code-box',
		'cicon-coin',
		'cicon-coin-o',
		'cicon-comment',
		'cicon-comment-o',
		'cicon-community',
		'cicon-community-o',
		'cicon-countdown',
		'cicon-countdown-o',
		'cicon-creative',
		'cicon-creative-o',
		'cicon-crop',
		'cicon-crown',
		'cicon-crown-o',
		'cicon-cut',
		'cicon-DarkMode',
		'cicon-dashboard',
		'cicon-delete',
		'cicon-delete-close',
		'cicon-delete-line',
		'cicon-delete-line-o',
		'cicon-delete-o',
		'cicon-deliver',
		'cicon-deliver-o',
		'cicon-demo',
		'cicon-discover',
		'cicon-discover-o',
		'cicon-discuss-fill',
		'cicon-discuss-line',
		'cicon-dollar',
		'cicon-dollar-o',
		'cicon-done',
		'cicon-done-all',
		'cicon-douyin',
		'cicon-drop-down',
		'cicon-drop-up',
		'cicon-eject',
		'cicon-ellipse',
		'cicon-emoji',
		'cicon-emoji-o',
		'cicon-equalizer',
		'cicon-eraser',
		'cicon-eraser-o',
		'cicon-evaluate',
		'cicon-evaluate-o',
		'cicon-event-close',
		'cicon-event-done',
		'cicon-event-list',
		'cicon-explore',
		'cicon-explore-line',
		'cicon-explore-line-o',
		'cicon-explore-o',
		'cicon-extension',
		'cicon-extension-o',
		'cicon-eye',
		'cicon-eye-favor',
		'cicon-eye-favor-o',
		'cicon-eye-o',
		'cicon-eye-off',
		'cicon-eye-off-o',
		'cicon-facebook',
		'cicon-favorite',
		'cicon-favorite-o',
		'cicon-female',
		'cicon-file',
		'cicon-file-copy',
		'cicon-file-copy-o',
		'cicon-file-o',
		'cicon-file-text',
		'cicon-file-text-o',
		'cicon-filter',
		'cicon-fingerprint',
		'cicon-first-page',
		'cicon-flag',
		'cicon-flag-o',
		'cicon-flash-close',
		'cicon-flash-off',
		'cicon-flash-on',
		'cicon-flash-open',
		'cicon-folder',
		'cicon-folder-add',
		'cicon-folder-o',
		'cicon-folder-special',
		'cicon-forward',
		'cicon-fullscreen',
		'cicon-fullscreen-exit',
		'cicon-game',
		'cicon-game-o',
		'cicon-git-commit',
		'cicon-git-commit-o',
		'cicon-github',
		'cicon-github-circle',
		'cicon-goods',
		'cicon-goodsnew',
		'cicon-goodsnew-o',
		'cicon-goods-o',
		'cicon-GooglePlaylogo',
		'cicon-grid',
		'cicon-grid-o',
		'cicon-group',
		'cicon-group-o',
		'cicon-guanli',
		'cicon-headset',
		'cicon-headset-mic',
		'cicon-help',
		'cicon-help-o',
		'cicon-home',
		'cicon-home-2',
		'cicon-home-2-o',
		'cicon-home-3',
		'cicon-home-3-o',
		'cicon-home-4',
		'cicon-home-4-o',
		'cicon-home-community',
		'cicon-home-dot',
		'cicon-home-dot-o',
		'cicon-home-line',
		'cicon-home-line-o',
		'cicon-home-o',
		'cicon-home-sm',
		'cicon-home-smile',
		'cicon-home-smile-o',
		'cicon-home-smline',
		'cicon-home-smline-o',
		'cicon-home-sm-o',
		'cicon-hotel',
		'cicon-hotel-o',
		'cicon-huohu',
		'cicon-IE',
		'cicon-image-text',
		'cicon-image-text-o',
		'cicon-import-export',
		'cicon-info',
		'cicon-info-o',
		'cicon-input',
		'cicon-input-o',
		'cicon-keyboard',
		'cicon-kinds',
		'cicon-last-page',
		'cicon-layout',
		'cicon-layout-o',
		'cicon-LightMode',
		'cicon-link',
		'cicon-link-off',
		'cicon-loader-fill',
		'cicon-loading',
		'cicon-loading1',
		'cicon-loading2',
		'cicon-location-off',
		'cicon-location-off-o',
		'cicon-location-on',
		'cicon-location-on-o',
		'cicon-lock',
		'cicon-lock-o',
		'cicon-lock-open',
		'cicon-logout',
		'cicon-loop',
		'cicon-magic',
		'cicon-magic-o',
		'cicon-mail',
		'cicon-mail-o',
		'cicon-male',
		'cicon-mic',
		'cicon-mic-none',
		'cicon-mic-off',
		'cicon-miniprogram',
		'cicon-mobile',
		'cicon-mobile-o',
		'cicon-moneybag',
		'cicon-moneybag-o',
		'cicon-more',
		'cicon-more-tag',
		'cicon-move',
		'cicon-move-round',
		'cicon-move-round-o',
		'cicon-music',
		'cicon-music-off',
		'cicon-my',
		'cicon-my-o',
		'cicon-near-me',
		'cicon-near-me-o',
		'cicon-not',
		'cicon-notice',
		'cicon-notice-active',
		'cicon-notice-active-o',
		'cicon-notice-o',
		'cicon-notice-off',
		'cicon-notice-off-o',
		'cicon-numcode',
		'cicon-order',
		'cicon-order-o',
		'cicon-paint',
		'cicon-paint-o',
		'cicon-palette',
		'cicon-palette-o',
		'cicon-pause',
		'cicon-pause-circle',
		'cicon-person',
		'cicon-person-add',
		'cicon-person-add-o',
		'cicon-person-o',
		'cicon-person-pin-circle',
		'cicon-person-pin-circle-o',
		'cicon-phone',
		'cicon-phone-call',
		'cicon-pic',
		'cicon-pic-o',
		'cicon-pin-drop',
		'cicon-pin-drop-o',
		'cicon-place',
		'cicon-place-o',
		'cicon-play-arrow',
		'cicon-play-circle',
		'cicon-play-circle-o',
		'cicon-popover',
		'cicon-popover-o',
		'cicon-present',
		'cicon-present-o',
		'cicon-progress',
		'cicon-qq',
		'cicon-qr-code-fill',
		'cicon-qr-code-line',
		'cicon-quill',
		'cicon-quill-o',
		'cicon-radio',
		'cicon-radiobox',
		'cicon-radiobox-o',
		'cicon-recharge',
		'cicon-recharge-o',
		'cicon-record',
		'cicon-record-o',
		'cicon-redo',
		'cicon-redpacket',
		'cicon-redpacket-o',
		'cicon-refresh',
		'cicon-repair',
		'cicon-repair-o',
		'cicon-repeat',
		'cicon-replay',
		'cicon-reply',
		'cicon-reply-all',
		'cicon-road-map',
		'cicon-road-map-o',
		'cicon-round',
		'cicon-round-angle',
		'cicon-round-angle-o',
		'cicon-round-arrow-line',
		'cicon-round-box',
		'cicon-safe',
		'cicon-safe-check',
		'cicon-safe-check-o',
		'cicon-safe-flash',
		'cicon-safe-flash-o',
		'cicon-safe-key',
		'cicon-safe-key-o',
		'cicon-safe-o',
		'cicon-save',
		'cicon-save-o',
		'cicon-scan',
		'cicon-scissors',
		'cicon-search',
		'cicon-search-line',
		'cicon-searchlist',
		'cicon-search-o',
		'cicon-search-sm',
		'cicon-service',
		'cicon-service-fill',
		'cicon-service-o',
		'cicon-set',
		'cicon-set-list',
		'cicon-set-o',
		'cicon-settings',
		'cicon-settings-o',
		'cicon-share',
		'cicon-share-line-o',
		'cicon-shengji',
		'cicon-shopping-cart',
		'cicon-shopping-cart-o',
		'cicon-show',
		'cicon-show-o',
		'cicon-shuffle',
		'cicon-sip',
		'cicon-sip-o',
		'cicon-skip-next',
		'cicon-skip-previous',
		'cicon-slack',
		'cicon-slack-square',
		'cicon-sort',
		'cicon-sort-order',
		'cicon-sound',
		'cicon-sponsor',
		'cicon-sponsor-o',
		'cicon-star',
		'cicon-star-half',
		'cicon-star-o',
		'cicon-stock',
		'cicon-stop',
		'cicon-store',
		'cicon-store-0',
		'cicon-store-2',
		'cicon-store-2-o',
		'cicon-sub-left',
		'cicon-sub-right',
		'cicon-subtitles',
		'cicon-subtitles-o',
		'cicon-sync-alt',
		'cicon-tag',
		'cicon-tag-o',
		'cicon-taobao',
		'cicon-terminal',
		'cicon-terminal-o',
		'cicon-thumb-down',
		'cicon-thumb-down-o',
		'cicon-thumb-up',
		'cicon-thumb-up-line',
		'cicon-thumb-up-line-o',
		'cicon-thumb-up-o',
		'cicon-ticket',
		'cicon-ticket-o',
		'cicon-time',
		'cicon-time-o',
		'cicon-timer',
		'cicon-title',
		'cicon-titles',
		'cicon-toggle',
		'cicon-toggle-o',
		'cicon-topbar',
		'cicon-translate',
		'cicon-tree',
		'cicon-Tt',
		'cicon-twiter',
		'cicon-cicon-community-o',
		'cicon-undo',
		'cicon-unfold-less',
		'cicon-unfold-more',
		'cicon-upstage',
		'cicon-upstage-o',
		'cicon-view-agenda',
		'cicon-view-array',
		'cicon-view-carousel',
		'cicon-view-column',
		'cicon-view-day',
		'cicon-view-headline',
		'cicon-view-list',
		'cicon-view-module',
		'cicon-view-quilt',
		'cicon-volume',
		'cicon-volume-off',
		'cicon-warn',
		'cicon-warn-o',
		'cicon-wechat-pay',
		'cicon-weibo-fill',
		'cicon-weibo-o',
		'cicon-weixin',
		'cicon-whatshot',
		'cicon-whatshot-o',
		'cicon-wifi',
		'cicon-wifi-off',
		'cicon-yamaxun',
		'cicon-zuoji',
	];
</script>
