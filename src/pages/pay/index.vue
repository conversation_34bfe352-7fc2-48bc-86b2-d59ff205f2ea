<!-- 收银台 -->
<template>
	<s-layout title="收银台">
		<view class="bg-white ss-modal-box ss-flex-col">
			<view class="modal-header ss-flex-col ss-col-center ss-row-center">
				<view class="money-box ss-m-b-20">
					<text class="money-text">{{ state.totalAmount }}</text>
				</view>
				<view class="time-text">
					<text>{{ payDescText }}</text>
				</view>
			</view>
			<view class="modal-content ss-flex-1">
				<view class="pay-title ss-p-l-30 ss-m-y-30">选择支付方式</view>
				<radio-group @change="onTapPay">
					<label class="pay-type-item" v-for="item in state.payMethods" :key="item.title">
						<view class="pay-item ss-flex ss-col-center ss-row-between ss-p-x-30 border-bottom" :class="{ 'disabled-pay-item': item.disabled }" v-if="allowedPayment.includes(item.value)">
							<view class="ss-flex ss-col-center">
								<image class="pay-icon" v-if="item.disabled" :src="sheep.$url.static('/static/img/shop/pay/cod_disabled.png')" mode="aspectFit"></image>
								<image class="pay-icon" v-else :src="item.icon" mode="aspectFit"></image>
								<text class="pay-title">{{ item.title }}</text>
							</view>
							<view class="check-box ss-flex ss-col-center ss-p-l-10">
								<view class="userInfo-money ss-m-r-10" v-if="item.value == 'money'"> 余额: {{ userInfo.money }}元 </view>
								<view class="userInfo-money ss-m-r-10" v-if="item.value == 'offline' && item.disabled"> 部分商品不支持 </view>
								<radio :value="item.value" color="var(--ui-BG-Main)" style="transform: scale(0.8)" :disabled="item.disabled" :checked="state.payment === item.value" />
							</view>
						</view>
					</label>
				</radio-group>
			</view>
			<!-- 工具 -->
			<view class="modal-footer ss-flex ss-row-center ss-col-center ss-m-t-80 ss-m-b-40">
				<button v-if="state.payStatus === 0" class="ss-reset-button past-due-btn">检测支付环境中</button>
				<button v-else-if="state.payStatus === -1" class="ss-reset-button past-due-btn" disabled>支付已过期</button>
				<button v-else class="ss-reset-button save-btn" @tap="onPay" :disabled="state.payStatus !== 1" :class="{ 'disabled-btn': state.payStatus !== 1 }">立即支付</button>
			</view>

			<!-- 复制订单号 -->
			<view class="copy-order-container" v-if="userInfo.spreadUid && userInfo.isBindSpread == 1">
				<view class="order-sn-title">订单信息</view>
				<view class="copy-order-sn ss-flex ss-col-center">
					<view class="order-info">
						<text class="order-label">支付单号</text>
						<text class="order-id">{{ state.payId }}</text>
					</view>
					<view class="copy-btn-wrapper" @tap="onCopy(state.payId)">
						<view class="copy-btn ss-flex ss-col-center ss-row-center">
							<text class="copy-text">复制</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 我的接单员信息 -->
			<view class="my-order-agent-info" v-if="userInfo.spreadUid && userInfo.isBindSpread == 1">
				咨询我的接单员<text class="my-order-agent-info-link" @tap="callPhone">立即咨询</text>
			</view>
		</view>
	</s-layout>
</template>
<script setup>
	import { computed, reactive } from 'vue';
	import { onLoad } from '@dcloudio/uni-app';
	import sheep from '@/sheep';
	import { Base64 } from 'js-base64';

	const userInfo = computed(() => sheep.$store('user').userInfo);

	// 检测支付环境
	const state = reactive({
		orderType: 'goods',
		payment: 'wechat',
		totalAmount: '',
		orderInfo: {},
		payStatus: 0, // 0=检测支付环境, -2=未查询到支付单信息， -1=支付已过期， 1=待支付，2=订单已支付
		payMethods: [],
		payId: '',
	});

	const allowedPayment = computed(() => {
		if (state.orderType === 'recharge') {
			return sheep.$store('app').platform.recharge_payment;
		}
		return sheep.$store('app').platform.payment;
	});

	const payMethods = [
		{
			icon: '/static/pay/wechat.png',
			title: '微信支付',
			value: 'wechat',
			disabled: false,
		},
		{
			icon: '/static/img/shop/pay/alipay.png',
			title: '支付宝支付',
			value: 'alipay',
			disabled: false,
		},
		{
			icon: '/static/img/shop/pay/wallet.png',
			title: '余额支付',
			value: 'money',
			disabled: false,
		},
		{
			icon: '/static/img/shop/pay/apple.png',
			title: 'Apple Pay',
			value: 'apple',
			disabled: false,
		},
		{
			icon: '/static/img/shop/pay/cod.png',
			title: '货到付款',
			value: 'offline',
			disabled: false,
		},
	];

	const onCopy = (data) => {
		let infos = '订单号：' + data + '\n';
		if (userInfo.value.spreadUid && userInfo.value.isBindSpread == 1) {
			infos += '用户昵称：' + userInfo.value.nickname + '\n';
			infos += '联系方式：' + userInfo.value.phoneHidden + '\n';
		}
		sheep.$helper.copyText(infos);
		sheep.$helper.toast('复制成功');
	};

	const onPay = () => {
		if (sheep.$platform.name === 'H5') {
			sheep.$helper.toast('请在微信中打开支付');
			return;
		}
		if (state.payment === '') {
			sheep.$helper.toast('请选择支付方式');
			return;
		}
		if (state.payment === 'wechat') {
			uni.showModal({
				title: '提示',
				content: '确定要支付吗?',
				success: function (res) {
					if (res.confirm) {
						sheep.$platform.pay(state.payment, state.orderType, state.payId, state.totalAmount);
					}
				},
			});
		}
	};

	const payDescText = computed(() => {
		// if (state.payStatus === 2) {
		//   return '该订单已支付';
		// }
		// if (state.payStatus === 1 && state.orderInfo.ext.expired_time !== 0) {
		//   const time = useDurationTime(state.orderInfo.ext.expired_time);
		//   if (time.ms <= 0) {
		//     state.payStatus = -1;
		//     return '';
		//   }
		//   return `剩余支付时间 ${time.h}:${time.m}:${time.s} `;
		// }
		// if (state.payStatus === -2) {
		//   return '未查询到支付单信息';
		// }
		// return '';
	});

	// 拨打电话
	const callPhone = async () => {
		const { code, data } = await sheep.$api.user.getOrderTakerInfo();
		if (code == 200) {
			const res = await sheep.$api.user.decryptPhone(data.phoneEncrypted);
			if (res) sheep.$helper.tools.callPhone(res);
		} else sheep.$helper.toast('暂无联系方式');
	};


	function checkPayStatus() {
		if (state.orderInfo.status === 'unpaid') {
			state.payStatus = 1;
			return;
		}
		if (state.orderInfo.status === 'closed') {
			state.payStatus = -1;
			return;
		}
		state.payStatus = 2;
	}

	function onTapPay(e) {
		state.payment = e.detail.value;
	}

	async function setRechargeOrder(id) {
		const { data, error } = await sheep.$api.trade.order(id);
		if (error === 0) {
			state.orderInfo = data;
			state.payMethods = payMethods;
			checkPayStatus();
		} else {
			state.payStatus = -2;
		}
	}

	async function setGoodsOrder(id) {
		state.payStatus = 1;
		state.payMethods = payMethods.filter((it) => it.value === 'wechat');
	}

	onLoad(async (options) => {
		const authInfo = sheep.$store('app').authInfo;
		if (!sheep.$store('user').userInfo.openId && authInfo) {
			await sheep.$api.user.setWechatInfo(Base64.encode(JSON.stringify(authInfo)));
			sheep.$store('user').userInfo.openId = authInfo.openid;
			sheep.$store('app').authInfo = null;
		}
		// if (
		//   sheep.$platform.name === 'WechatOfficialAccount' &&
		//   sheep.$platform.os === 'ios' &&
		//   !sheep.$platform.landingPage.includes('pages/pay/index')
		// ) {
		//   location.reload();
		//   return;
		// }
		let id = '';
		if (options.orderSN) {
			id = options.orderSN;
		}
		if (options.id) {
			id = options.id;
		}
		if (options.totalAmount) {
			state.totalAmount = options.totalAmount;
		}
		state.payId = id;
		if (options.type === 'recharge') {
			state.orderType = 'recharge';
			// 充值订单
			setRechargeOrder(id);
		} else {
			state.orderType = 'goods';
			// 商品订单
			setGoodsOrder(id);
		}
	});
</script>

<style lang="scss" scoped>
	.pay-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 26rpx;
	}

	.ss-modal-box {
		// max-height: 1000rpx;

		.modal-header {
			position: relative;
			padding: 60rpx 20rpx 40rpx;
			background: linear-gradient(to bottom, rgba(var(--ui-BG-Main-rgb), 0.05), rgba(var(--ui-BG-Main-rgb), 0));
			border-bottom: 1rpx solid #f5f5f5;

			.money-text {
				color: $red;
				font-size: 52rpx;
				font-weight: bold;
				font-family: OPPOSANS;
				letter-spacing: 1rpx;

				&::before {
					content: '￥';
					font-size: 34rpx;
				}
			}

			.time-text {
				font-size: 26rpx;
				color: $gray-b;
			}

			.close-icon {
				position: absolute;
				top: 10rpx;
				right: 20rpx;
				font-size: 46rpx;
				opacity: 0.2;
			}
		}

		.modal-content {
			overflow-y: auto;

			.pay-title {
				font-size: 28rpx;
				font-weight: 500;
				color: #333333;
				position: relative;
				padding-left: 20rpx;
			}

			.pay-tip {
				font-size: 26rpx;
				color: #bbbbbb;
			}

			.pay-item {
				height: 86rpx;
			}
			.disabled-pay-item {
				.pay-title {
					color: #999999;
				}
			}

			.userInfo-money {
				font-size: 26rpx;
				color: #bbbbbb;
				line-height: normal;
			}
		}

		.save-btn {
			width: 710rpx;
			height: 90rpx;
			border-radius: 45rpx;
			background: linear-gradient(90deg, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
			color: $white;
			font-size: 32rpx;
			font-weight: 500;
			letter-spacing: 2rpx;
			box-shadow: 0 8rpx 16rpx rgba(var(--ui-BG-Main-rgb), 0.25);
			transition: all 0.2s;
			
			&:active {
				transform: translateY(2rpx);
				box-shadow: 0 4rpx 8rpx rgba(var(--ui-BG-Main-rgb), 0.2);
			}
		}
		.disabled-btn {
			background: #e5e5e5;
			color: #999999;
			box-shadow: none;
		}

		.past-due-btn {
			width: 710rpx;
			height: 90rpx;
			border-radius: 45rpx;
			background-color: #999;
			color: #fff;
			font-size: 32rpx;
		}
	}

	.copy-order-container {
		margin: 30rpx 30rpx 40rpx;
		
		.order-sn-title {
			font-size: 28rpx;
			font-weight: 500;
			color: #333;
			margin-bottom: 20rpx;
			position: relative;
			padding-left: 24rpx;
			
			&::before {
				content: '';
				position: absolute;
				left: 0;
				top: 50%;
				transform: translateY(-50%);
				width: 6rpx;
				height: 24rpx;
				background-color: var(--ui-BG-Main);
				border-radius: 3rpx;
			}
		}
		
		.copy-order-sn {
			background: linear-gradient(to right, #fafafa, #f8f8f8);
			border-radius: 16rpx;
			padding: 30rpx;
			justify-content: space-between;
			box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.06);
			border: 1rpx solid rgba(0, 0, 0, 0.03);
			position: relative;
			overflow: hidden;
			
			&::before {
				content: '';
				position: absolute;
				left: 0;
				top: 0;
				width: 100%;
				height: 6rpx;
				background: linear-gradient(to right, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
				opacity: 0.7;
			}
			
			.order-info {
				display: flex;
				flex-direction: column;
				
				.order-label {
					font-size: 24rpx;
					color: #999;
					margin-bottom: 10rpx;
					position: relative;
					padding-left: 18rpx;
					
					&::before {
						content: '';
						position: absolute;
						left: 0;
						top: 50%;
						transform: translateY(-50%);
						width: 8rpx;
						height: 8rpx;
						border-radius: 50%;
						background-color: var(--ui-BG-Main);
					}
				}
				
				.order-id {
					font-size: 32rpx;
					color: #333;
					font-weight: 500;
					font-family: OPPOSANS;
					letter-spacing: 1rpx;
					border-radius: 6rpx;
					background-color: rgba(var(--ui-BG-Main-rgb), 0.05);
					padding: 6rpx 12rpx;
				}
			}
			
			.copy-btn-wrapper {
				.copy-btn {
					background: linear-gradient(90deg, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
					border-radius: 30rpx;
					padding: 14rpx 28rpx;
					transition: all 0.2s;
					box-shadow: 0 4rpx 8rpx rgba(var(--ui-BG-Main-rgb), 0.2);
					
					&:active {
						opacity: 0.85;
						transform: scale(0.98);
					}
					
					.copy-text {
						font-size: 26rpx;
						color: #fff;
						font-weight: 500;
					}
				}
			}
		}
	}

	.my-order-agent-info {
		margin: 0 30rpx 30rpx;
		padding: 20rpx 30rpx;
		background: linear-gradient(to right, #f9f9f9, #f5f5f5);
		border-radius: 16rpx;
		font-size: 28rpx;
		color: #666;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.03);
		position: relative;
		display: flex;
		align-items: center;
		justify-content: space-between;
		
		&::before {
			content: '';
			position: absolute;
			left: 0;
			top: 0;
			height: 100%;
			width: 8rpx;
			background: linear-gradient(to bottom, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
			border-top-left-radius: 16rpx;
			border-bottom-left-radius: 16rpx;
		}
		
		.my-order-agent-info-link {
			color: var(--ui-BG-Main);
			font-weight: 500;
			margin-left: 20rpx;
			position: relative;
			padding-right: 30rpx;
			
			&::after {
				content: '';
				position: absolute;
				right: 0;
				top: 50%;
				transform: translateY(-50%);
				width: 16rpx;
				height: 16rpx;
				border-top: 2rpx solid var(--ui-BG-Main);
				border-right: 2rpx solid var(--ui-BG-Main);
				transform: translateY(-50%) rotate(45deg);
			}
		}
	}
</style>
